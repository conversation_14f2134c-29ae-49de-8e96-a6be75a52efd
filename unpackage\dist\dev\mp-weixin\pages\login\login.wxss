/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
/* ==================== 主容器 ==================== */
.login-container.data-v-e4e4508d {
  min-height: 100vh;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  position: relative;
}

/* ==================== 头部区域 ==================== */
.login-header.data-v-e4e4508d {
  text-align: center;
  margin-bottom: 96rpx;
  animation: fadeInUp-e4e4508d 0.8s ease-out;
}
.logo.data-v-e4e4508d {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 32rpx;
  border-radius: 50%;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.app-name.data-v-e4e4508d {
  display: block;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.app-desc.data-v-e4e4508d {
  display: block;
  opacity: 0.9;
}

/* ==================== 表单区域 ==================== */
.login-form.data-v-e4e4508d {
  width: 100%;
  max-width: 640rpx;
  animation: fadeInUp-e4e4508d 0.8s ease-out 0.2s both;
}

/* ==================== 协议区域 ==================== */
.agreement-section.data-v-e4e4508d {
  margin-bottom: 48rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.agreement-text.data-v-e4e4508d {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  line-height: 1.6;
  margin-left: 16rpx;
}
.agreement-link.data-v-e4e4508d {
  text-decoration: underline;
  margin: 0 4rpx;
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp-e4e4508d {
from {
    opacity: 0;
    transform: translateY(60rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 750rpx) {
.login-container.data-v-e4e4508d {
    padding: 32rpx;
}
.logo.data-v-e4e4508d {
    width: 140rpx;
    height: 140rpx;
}
}
/* ==================== 深色模式适配 ==================== */
@media (prefers-color-scheme: dark) {
.agreement-section.data-v-e4e4508d {
    background: rgba(0, 0, 0, 0.2);
}
}