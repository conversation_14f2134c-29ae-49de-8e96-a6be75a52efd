"use strict";
const src_utils_request = require("../../utils/request.js");
function getAnnouncementList(params) {
  return src_utils_request.http.get("/info/announcements", params);
}
function getPolicyList(params) {
  return src_utils_request.http.get("/info/policies", params);
}
function getNoticeList(params) {
  return src_utils_request.http.get("/info/notices", params);
}
exports.getAnnouncementList = getAnnouncementList;
exports.getNoticeList = getNoticeList;
exports.getPolicyList = getPolicyList;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/api/modules/info.js.map
