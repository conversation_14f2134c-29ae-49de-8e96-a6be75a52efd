<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>dropdown</title>
</head>
<body>
    <!-- 1. Define some markup -->
    <button type="button">Select</button>
    <select>
        <option>Option 1</option>
        <option selected>Option 2</option>
        <option>Option 3</option>
    </select>

    <!-- 2. Include library -->
    <script src="../dist/select.js"></script>

    <!-- 3. Select! -->
    <script>
    var dropdown = document.querySelector('select');
    var button = document.querySelector('button');

    button.addEventListener('click', function(e) {
        var selected = select(dropdown);
        console.log(selected);
    });
    </script>
</body>
</html>
