"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_user = require("../../src/api/modules/user.js");
if (!Array) {
  const _component_uv_icon = common_vendor.resolveComponent("uv-icon");
  const _component_uv_button = common_vendor.resolveComponent("uv-button");
  (_component_uv_icon + _component_uv_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const { setProfile } = userStore;
    const agreedToTerms = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const appName = common_vendor.computed(() => "疾控医护考试系统");
    const appDescription = common_vendor.computed(() => "专业、权威、便捷的任职资格考试平台");
    const loginButtonStyle = common_vendor.computed(() => ({
      width: "100%",
      height: "88rpx",
      backgroundColor: "#ffffff",
      color: agreedToTerms.value ? "#1976d2" : "rgba(25, 118, 210, 0.5)",
      fontSize: "32rpx",
      fontWeight: "500",
      borderRadius: "16rpx"
    }));
    function onAgreementChange(value) {
      agreedToTerms.value = value.length > 0;
    }
    function showUserAgreement() {
      common_vendor.index.showModal({
        title: "用户服务协议",
        content: `
1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。
    `,
        showCancel: false,
        confirmText: "我知道了"
      });
    }
    function showPrivacyPolicy() {
      common_vendor.index.showModal({
        title: "隐私政策",
        content: `
1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。
    `,
        showCancel: false,
        confirmText: "我知道了"
      });
    }
    async function handleWxLogin() {
      if (!agreedToTerms.value) {
        common_vendor.index.showToast({
          title: "请先同意用户协议",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      isLoading.value = true;
      try {
        const loginResult = await new Promise((resolve, reject) => {
          common_vendor.index.login({
            provider: "weixin",
            success: resolve,
            fail: reject
          });
        });
        const loginParams = {
          code: loginResult.code
        };
        const userInfo = await src_api_modules_user.wxLogin(loginParams);
        setProfile(userInfo);
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          navigateByUserStatus(userInfo.status);
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:176", "微信登录失败:", error);
        common_vendor.index.showToast({
          title: "登录失败，请重试",
          icon: "none",
          duration: 2e3
        });
      } finally {
        isLoading.value = false;
      }
    }
    function navigateByUserStatus(status) {
      switch (status) {
        case "approved":
          common_vendor.index.reLaunch({ url: "/pages/info/info" });
          break;
        case "pending":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "rejected":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "incomplete":
        default:
          common_vendor.index.navigateTo({ url: "/pages/register/register" });
          break;
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0,
        b: common_vendor.t(appName.value),
        c: common_vendor.t(appDescription.value),
        d: agreedToTerms.value,
        e: common_vendor.o(showUserAgreement),
        f: common_vendor.o(showPrivacyPolicy),
        g: common_vendor.o(onAgreementChange),
        h: common_vendor.p({
          name: "weixin",
          color: "#1976d2",
          size: "40"
        }),
        i: common_vendor.o(handleWxLogin),
        j: common_vendor.p({
          type: "primary",
          disabled: !agreedToTerms.value || isLoading.value,
          loading: isLoading.value,
          loadingText: "登录中...",
          customStyle: loginButtonStyle.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
