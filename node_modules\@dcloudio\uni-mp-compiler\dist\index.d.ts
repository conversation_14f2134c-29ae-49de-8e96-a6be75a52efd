import { type CodegenResult, type ParserOptions, type RootNode } from '@vue/compiler-core';
import type { CompilerOptions } from './options';
export type { CompilerOptions } from './options';
export { findProp } from '@vue/compiler-core';
export type { DirectiveNode, NodeTransform, DirectiveTransform, TransformContext, SimpleExpressionNode, } from '@vue/compiler-core';
export { genExpr } from './codegen';
export { rewriteExpression } from './transforms/utils';
export { isForElementNode } from './transforms/vFor';
export { transformOn } from './transforms/vOn';
export { transformModel } from './transforms/vModel';
export * from './runtimeHelpers';
export declare function parse(template: string, options?: ParserOptions): RootNode;
export declare function compile(template: string, options?: CompilerOptions): CodegenResult;
