"use strict";
const common_vendor = require("../../../common/vendor.js");
const useUserStore = common_vendor.defineStore("user", () => {
  const profile = common_vendor.ref(null);
  const isLoggedIn = common_vendor.computed(() => {
    var _a;
    return !!((_a = profile.value) == null ? void 0 : _a.token);
  });
  const token = common_vendor.computed(() => {
    var _a;
    return ((_a = profile.value) == null ? void 0 : _a.token) || "";
  });
  const userStatus = common_vendor.computed(() => {
    var _a;
    return ((_a = profile.value) == null ? void 0 : _a.status) || "incomplete";
  });
  const isApproved = common_vendor.computed(() => {
    var _a;
    return ((_a = profile.value) == null ? void 0 : _a.status) === "approved";
  });
  const isPending = common_vendor.computed(() => {
    var _a;
    return ((_a = profile.value) == null ? void 0 : _a.status) === "pending";
  });
  const isRejected = common_vendor.computed(() => {
    var _a;
    return ((_a = profile.value) == null ? void 0 : _a.status) === "rejected";
  });
  const isIncomplete = common_vendor.computed(() => {
    var _a;
    return ((_a = profile.value) == null ? void 0 : _a.status) === "incomplete";
  });
  function setProfile(userInfo) {
    profile.value = userInfo;
    common_vendor.index.setStorageSync("user_profile", userInfo);
  }
  function updateProfile(updates) {
    if (profile.value) {
      profile.value = { ...profile.value, ...updates };
      common_vendor.index.setStorageSync("user_profile", profile.value);
    }
  }
  function clearProfile() {
    profile.value = null;
    common_vendor.index.removeStorageSync("user_profile");
  }
  function initProfile() {
    try {
      const savedProfile = common_vendor.index.getStorageSync("user_profile");
      if (savedProfile) {
        profile.value = savedProfile;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/modules/user.ts:47", "初始化用户信息失败:", error);
    }
  }
  return {
    // State
    profile,
    // Getters
    isLoggedIn,
    token,
    userStatus,
    isApproved,
    isPending,
    isRejected,
    isIncomplete,
    // Actions
    setProfile,
    updateProfile,
    clearProfile,
    initProfile
  };
});
exports.useUserStore = useUserStore;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/stores/modules/user.js.map
