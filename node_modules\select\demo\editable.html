<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>editable</title>
</head>
<body>
    <!-- 1. Define some markup -->
    <button type="button">Select</button>
    <input type="text" value="Lorem ipsum">

    <!-- 2. Include library -->
    <script src="../dist/select.js"></script>

    <!-- 3. Select! -->
    <script>
    var input = document.querySelector('input');
    var button = document.querySelector('button');

    button.addEventListener('click', function(e) {
        var selected = select(input);
        console.log(selected);
    });
    </script>
</body>
</html>
