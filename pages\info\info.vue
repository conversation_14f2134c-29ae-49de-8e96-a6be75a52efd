<template>
  <view class="info-container">
    <!-- 非正式用户状态提示 -->
    <view v-if="!userStore.isApproved" class="status-tip">
      <view class="tip-content">
        <text class="tip-icon">⚠️</text>
        <view class="tip-text">
          <text class="tip-title">{{ getStatusTipTitle() }}</text>
          <text class="tip-desc">{{ getStatusTipDesc() }}</text>
        </view>
        <button v-if="userStore.isIncomplete" class="tip-btn" @tap="goToRegister">
          去完善资料
        </button>
      </view>
    </view>
    
    <!-- 正式用户内容 -->
    <view v-else class="content-section">
      <!-- 轮播公告区 -->
      <view class="banner-section">
        <swiper class="banner-swiper" indicator-dots circular autoplay>
          <swiper-item v-for="item in bannerList" :key="item.id">
            <view class="banner-item" @tap="goToDetail(item)">
              <text class="banner-title">{{ item.title }}</text>
            </view>
          </swiper-item>
        </swiper>
      </view>
      
      <!-- 信息列表 -->
      <view class="info-list">
        <!-- 公告列表 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">📢 最新公告</text>
            <text class="section-more" @tap="goToList('announcement')">更多</text>
          </view>
          <view class="info-items">
            <view 
              v-for="item in announcementList" 
              :key="item.id"
              class="info-item"
              @tap="goToDetail(item)"
            >
              <view class="item-header">
                <text class="item-title">{{ item.title }}</text>
                <text v-if="item.isTop" class="top-tag">置顶</text>
              </view>
              <text class="item-time">{{ formatTime(item.publishTime) }}</text>
            </view>
          </view>
        </view>
        
        <!-- 政策法规 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">📋 政策法规</text>
            <text class="section-more" @tap="goToList('policy')">更多</text>
          </view>
          <view class="info-items">
            <view 
              v-for="item in policyList" 
              :key="item.id"
              class="info-item"
              @tap="goToDetail(item)"
            >
              <view class="item-header">
                <text class="item-title">{{ item.title }}</text>
              </view>
              <text class="item-time">{{ formatTime(item.publishTime) }}</text>
            </view>
          </view>
        </view>
        
        <!-- 重要通知 -->
        <view class="info-section">
          <view class="section-header">
            <text class="section-title">🔔 重要通知</text>
            <text class="section-more" @tap="goToList('notice')">更多</text>
          </view>
          <view class="info-items">
            <view 
              v-for="item in noticeList" 
              :key="item.id"
              class="info-item"
              @tap="goToDetail(item)"
            >
              <view class="item-header">
                <text class="item-title">{{ item.title }}</text>
              </view>
              <text class="item-time">{{ formatTime(item.publishTime) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useUserStore } from '../../src/stores/modules/user';
import { storeToRefs } from 'pinia';
import { getAnnouncementList, getPolicyList, getNoticeList } from '../../src/api/modules/info';
import type { InfoItem } from '../../src/types/api';

const userStore = useUserStore();
const { isApproved, isIncomplete, isPending, isRejected } = storeToRefs(userStore);

// 响应式数据
const bannerList = ref<InfoItem[]>([]);
const announcementList = ref<InfoItem[]>([]);
const policyList = ref<InfoItem[]>([]);
const noticeList = ref<InfoItem[]>([]);

onMounted(() => {
  if (userStore.isApproved) {
    loadInfoData();
  }
});

/**
 * 加载信息数据
 */
async function loadInfoData() {
  try {
    // 并行加载各类信息
    const [announcements, policies, notices] = await Promise.all([
      getAnnouncementList({ page: 1, pageSize: 5 }),
      getPolicyList({ page: 1, pageSize: 3 }),
      getNoticeList({ page: 1, pageSize: 3 }),
    ]);
    
    announcementList.value = announcements;
    policyList.value = policies;
    noticeList.value = notices;
    
    // 提取置顶公告作为轮播
    bannerList.value = announcements.filter(item => item.isTop).slice(0, 3);
  } catch (error) {
    console.error('加载信息数据失败:', error);
  }
}

/**
 * 获取状态提示标题
 */
function getStatusTipTitle() {
  if (userStore.isIncomplete) return '请完善个人资料';
  if (userStore.isPending) return '资料审核中';
  if (userStore.isRejected) return '资料审核未通过';
  return '身份认证异常';
}

/**
 * 获取状态提示描述
 */
function getStatusTipDesc() {
  if (userStore.isIncomplete) return '完善个人资料后即可查看最新信息';
  if (userStore.isPending) return '请耐心等待机构管理员审核';
  if (userStore.isRejected) return '请修改资料后重新提交';
  return '请联系管理员处理';
}

/**
 * 跳转到注册页面
 */
function goToRegister() {
  uni.navigateTo({ url: '/pages/register/register' });
}

/**
 * 跳转到信息列表
 */
function goToList(type: string) {
  uni.navigateTo({ url: `/pages/info/list?type=${type}` });
}

/**
 * 跳转到信息详情
 */
function goToDetail(item: InfoItem) {
  uni.navigateTo({ url: `/pages/info/detail?id=${item.id}` });
}

/**
 * 格式化时间
 */
function formatTime(timeStr: string) {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) return '今天';
  if (days === 1) return '昨天';
  if (days < 7) return `${days}天前`;
  
  return date.toLocaleDateString();
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.info-container {
  min-height: 100vh;
  background-color: $background-color;
}

.status-tip {
  margin: $spacing-lg;
  
  .tip-content {
    background-color: $surface-color;
    border-radius: $border-radius-medium;
    padding: $spacing-lg;
    display: flex;
    align-items: center;
    box-shadow: $shadow-light;
    
    .tip-icon {
      font-size: $font-size-xl;
      margin-right: $spacing-md;
    }
    
    .tip-text {
      flex: 1;
      
      .tip-title {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }
      
      .tip-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
    
    .tip-btn {
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: $border-radius-small;
      padding: $spacing-xs $spacing-sm;
      font-size: $font-size-sm;
    }
  }
}

.content-section {
  .banner-section {
    margin: $spacing-md;
    
    .banner-swiper {
      height: 200rpx;
      border-radius: $border-radius-medium;
      overflow: hidden;
      
      .banner-item {
        background: linear-gradient(135deg, $primary-color, $primary-light);
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: $spacing-md;
        
        .banner-title {
          color: white;
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          text-align: center;
        }
      }
    }
  }
  
  .info-list {
    .info-section {
      margin: $spacing-md;
      background-color: $surface-color;
      border-radius: $border-radius-medium;
      overflow: hidden;
      box-shadow: $shadow-light;
      
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: $spacing-md;
        border-bottom: 1rpx solid $divider-color;
        
        .section-title {
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          color: $text-primary;
        }
        
        .section-more {
          font-size: $font-size-sm;
          color: $primary-color;
        }
      }
      
      .info-items {
        .info-item {
          padding: $spacing-md;
          border-bottom: 1rpx solid $divider-color;
          
          &:last-child {
            border-bottom: none;
          }
          
          .item-header {
            display: flex;
            align-items: center;
            margin-bottom: $spacing-xs;
            
            .item-title {
              flex: 1;
              font-size: $font-size-md;
              color: $text-primary;
              line-height: $line-height-normal;
            }
            
            .top-tag {
              background-color: $error-color;
              color: white;
              font-size: $font-size-xs;
              padding: 2rpx 8rpx;
              border-radius: $border-radius-small;
              margin-left: $spacing-sm;
            }
          }
          
          .item-time {
            font-size: $font-size-xs;
            color: $text-disabled;
          }
        }
      }
    }
  }
}
</style>
