<view class="login-container data-v-e4e4508d"><view class="login-header data-v-e4e4508d"><image class="logo data-v-e4e4508d" src="{{a}}" mode="aspectFit"/><text class="app-name data-v-e4e4508d">{{b}}</text><text class="app-desc data-v-e4e4508d">{{c}}</text></view><view class="login-form data-v-e4e4508d"><view class="agreement-section data-v-e4e4508d"><checkbox-group class="data-v-e4e4508d" bindchange="{{g}}"><label class="agreement-item data-v-e4e4508d"><checkbox class="data-v-e4e4508d" checked="{{d}}" color="#4caf50"/><view class="agreement-text data-v-e4e4508d"><text class="data-v-e4e4508d">我已阅读并同意</text><text class="agreement-link data-v-e4e4508d" bindtap="{{e}}">《用户服务协议》</text><text class="data-v-e4e4508d">和</text><text class="agreement-link data-v-e4e4508d" bindtap="{{f}}">《隐私政策》</text></view></label></checkbox-group></view><uv-button wx:if="{{j}}" class="data-v-e4e4508d" u-s="{{['d']}}" bindclick="{{i}}" u-i="e4e4508d-0" bind:__l="__l" u-p="{{j}}"><uv-icon wx:if="{{h}}" class="data-v-e4e4508d" style="margin-right:16rpx" u-i="e4e4508d-1,e4e4508d-0" bind:__l="__l" u-p="{{h}}"/> 微信授权登录 </uv-button></view></view>