{"name": "@dcloudio/uni-h5-vite", "version": "3.0.0-alpha-4070220250613001", "description": "uni-h5-vite", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-h5-vite"}, "license": "Apache-2.0", "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da", "dependencies": {"@rollup/pluginutils": "^5.0.5", "@vue/compiler-dom": "3.4.21", "@vue/compiler-sfc": "3.4.21", "@vue/server-renderer": "3.4.21", "@vue/shared": "3.4.21", "debug": "^4.3.3", "fs-extra": "^10.0.0", "mime": "^3.0.0", "module-alias": "^2.2.2", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4070220250613001", "@dcloudio/uni-shared": "3.0.0-alpha-4070220250613001"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/fs-extra": "^9.0.13", "@types/mime": "^2.0.3", "@types/module-alias": "^2.0.4", "@types/resolve": "^1.20.2", "@vue/compiler-core": "3.4.21", "esbuild": "^0.20.1", "vue": "3.4.21"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}}