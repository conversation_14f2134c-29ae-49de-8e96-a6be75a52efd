"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../stores/modules/user.js");
const http = new common_vendor.Request({
  baseURL: "https://api.cdcexam.com/v1",
  // TODO: 替换为实际的API地址
  timeout: 1e4,
  header: {
    "Content-Type": "application/json"
  }
});
http.interceptors.request.use(
  (config) => {
    common_vendor.index.showLoading({
      title: "加载中...",
      mask: true
    });
    const userStore = src_stores_modules_user.useUserStore();
    if (userStore.token) {
      config.header.Authorization = `Bearer ${userStore.token}`;
    }
    return config;
  },
  (error) => {
    common_vendor.index.hideLoading();
    return Promise.reject(error);
  }
);
http.interceptors.response.use(
  (response) => {
    common_vendor.index.hideLoading();
    const res = response.data;
    if (res.code === 200) {
      return Promise.resolve(res.data);
    }
    if (res.code === 401) {
      const userStore = src_stores_modules_user.useUserStore();
      userStore.clearProfile();
      common_vendor.index.showToast({
        title: "登录已过期，请重新登录",
        icon: "none",
        duration: 2e3
      });
      setTimeout(() => {
        common_vendor.index.reLaunch({
          url: "/pages/login/login"
        });
      }, 2e3);
      return Promise.reject(res);
    }
    common_vendor.index.showToast({
      title: res.message || "请求失败",
      icon: "none",
      duration: 2e3
    });
    return Promise.reject(res);
  },
  (error) => {
    common_vendor.index.hideLoading();
    common_vendor.index.__f__("error", "at src/utils/request.ts:86", "HTTP请求错误:", error);
    let errorMessage = "网络错误，请稍后重试";
    if (error.statusCode) {
      switch (error.statusCode) {
        case 404:
          errorMessage = "请求的资源不存在";
          break;
        case 500:
          errorMessage = "服务器内部错误";
          break;
        case 502:
          errorMessage = "网关错误";
          break;
        case 503:
          errorMessage = "服务不可用";
          break;
        case 504:
          errorMessage = "网关超时";
          break;
        default:
          errorMessage = `请求失败 (${error.statusCode})`;
      }
    }
    common_vendor.index.showToast({
      title: errorMessage,
      icon: "none",
      duration: 2e3
    });
    return Promise.reject(error);
  }
);
exports.http = http;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/utils/request.js.map
