"use strict";
const src_utils_request = require("../../utils/request.js");
function getCurrentExams() {
  return src_utils_request.http.get("/exam/current");
}
function getExamHistory(page = 1, pageSize = 10) {
  return src_utils_request.http.get("/exam/history", { page, pageSize });
}
exports.getCurrentExams = getCurrentExams;
exports.getExamHistory = getExamHistory;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/api/modules/exam.js.map
