/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
.exam-container.data-v-970fed46 {
  min-height: 100vh;
  background-color: #fafafa;
}
.access-denied.data-v-970fed46 {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 48rpx;
}
.access-denied .denied-content.data-v-970fed46 {
  text-align: center;
}
.access-denied .denied-content .denied-icon.data-v-970fed46 {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}
.access-denied .denied-content .denied-title.data-v-970fed46 {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  color: #212121;
  margin-bottom: 16rpx;
}
.access-denied .denied-content .denied-desc.data-v-970fed46 {
  display: block;
  font-size: 28rpx;
  color: #757575;
  margin-bottom: 48rpx;
}
.access-denied .denied-content .denied-btn.data-v-970fed46 {
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}
.exam-content.data-v-970fed46 {
  padding: 24rpx;
}
.exam-content .exam-section.data-v-970fed46, .exam-content .history-section.data-v-970fed46 {
  margin-bottom: 48rpx;
}
.exam-content .exam-section .section-header.data-v-970fed46, .exam-content .history-section .section-header.data-v-970fed46 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.exam-content .exam-section .section-header .section-title.data-v-970fed46, .exam-content .history-section .section-header .section-title.data-v-970fed46 {
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
}
.exam-content .exam-section .section-header .section-more.data-v-970fed46, .exam-content .history-section .section-header .section-more.data-v-970fed46 {
  font-size: 24rpx;
  color: #1976d2;
}
.exam-content .exam-section .empty-state.data-v-970fed46, .exam-content .history-section .empty-state.data-v-970fed46 {
  text-align: center;
  padding: 48rpx;
}
.exam-content .exam-section .empty-state .empty-icon.data-v-970fed46, .exam-content .history-section .empty-state .empty-icon.data-v-970fed46 {
  display: block;
  font-size: 80rpx;
  margin-bottom: 24rpx;
}
.exam-content .exam-section .empty-state .empty-text.data-v-970fed46, .exam-content .history-section .empty-state .empty-text.data-v-970fed46 {
  font-size: 28rpx;
  color: #bdbdbd;
}
.exam-content .exam-list .exam-card.data-v-970fed46 {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.exam-content .exam-list .exam-card .exam-header.data-v-970fed46 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.exam-content .exam-list .exam-card .exam-header .exam-name.data-v-970fed46 {
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
}
.exam-content .exam-list .exam-card .exam-header .exam-type.data-v-970fed46 {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: white;
}
.exam-content .exam-list .exam-card .exam-header .exam-type.online.data-v-970fed46 {
  background-color: #1976d2;
}
.exam-content .exam-list .exam-card .exam-header .exam-type.offline.data-v-970fed46 {
  background-color: #4caf50;
}
.exam-content .exam-list .exam-card .exam-info.data-v-970fed46 {
  margin-bottom: 24rpx;
}
.exam-content .exam-list .exam-card .exam-info text.data-v-970fed46 {
  display: block;
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 8rpx;
}
.exam-content .exam-list .exam-card .exam-status.data-v-970fed46 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.exam-content .exam-list .exam-card .exam-status .status-text.data-v-970fed46 {
  font-size: 24rpx;
}
.exam-content .exam-list .exam-card .exam-status .status-text.not_started.data-v-970fed46 {
  color: #ff9800;
}
.exam-content .exam-list .exam-card .exam-status .status-text.passed.data-v-970fed46 {
  color: #4caf50;
}
.exam-content .exam-list .exam-card .exam-status .status-text.failed.data-v-970fed46 {
  color: #f44336;
}
.exam-content .exam-list .exam-card .exam-status .status-text.expired.data-v-970fed46 {
  color: #bdbdbd;
}
.exam-content .exam-list .exam-card .exam-status .exam-btn.data-v-970fed46 {
  border: none;
  border-radius: 4rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: white;
}
.exam-content .exam-list .exam-card .exam-status .exam-btn.online.data-v-970fed46 {
  background-color: #1976d2;
}
.exam-content .exam-list .exam-card .exam-status .exam-btn.offline.data-v-970fed46 {
  background-color: #4caf50;
}
.exam-content .history-list.data-v-970fed46 {
  background-color: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.exam-content .history-list .history-item.data-v-970fed46 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #e0e0e0;
}
.exam-content .history-list .history-item.data-v-970fed46:last-child {
  border-bottom: none;
}
.exam-content .history-list .history-item .history-info .history-name.data-v-970fed46 {
  display: block;
  font-size: 28rpx;
  color: #212121;
  margin-bottom: 8rpx;
}
.exam-content .history-list .history-item .history-info .history-time.data-v-970fed46 {
  font-size: 20rpx;
  color: #bdbdbd;
}
.exam-content .history-list .history-item .history-result.data-v-970fed46 {
  text-align: right;
}
.exam-content .history-list .history-item .history-result .history-score.data-v-970fed46 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.exam-content .history-list .history-item .history-result .history-score.passed.data-v-970fed46 {
  color: #4caf50;
}
.exam-content .history-list .history-item .history-result .history-score.failed.data-v-970fed46 {
  color: #f44336;
}
.exam-content .history-list .history-item .history-result .history-status.data-v-970fed46 {
  font-size: 20rpx;
}
.exam-content .history-list .history-item .history-result .history-status.passed.data-v-970fed46 {
  color: #4caf50;
}
.exam-content .history-list .history-item .history-result .history-status.failed.data-v-970fed46 {
  color: #f44336;
}