{"version": 3, "file": "study.js", "sources": ["pages/study/study.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3R1ZHkvc3R1ZHkudnVl"], "sourcesContent": ["<template>\n  <view class=\"study-container\">\n    <view class=\"study-header\">\n      <text class=\"page-title\">学习中心</text>\n      <text class=\"page-desc\">随时随地，高效学习</text>\n    </view>\n    \n    <view class=\"study-modules\">\n      <!-- 教材学习模块 -->\n      <view class=\"module-card\" @tap=\"handleTextbookClick\">\n        <view class=\"module-icon\">📚</view>\n        <view class=\"module-info\">\n          <text class=\"module-title\">教材学习</text>\n          <text class=\"module-desc\">系统化学习专业教材</text>\n          <text class=\"module-status\">功能建设中</text>\n        </view>\n        <view class=\"module-arrow\">></view>\n      </view>\n      \n      <!-- 题库练习模块 -->\n      <view class=\"module-card\" @tap=\"goToQuestionBank\">\n        <view class=\"module-icon\">📝</view>\n        <view class=\"module-info\">\n          <text class=\"module-title\">题库练习</text>\n          <text class=\"module-desc\">分类刷题，巩固知识</text>\n          <text class=\"module-status\">{{ getPracticeStatus() }}</text>\n        </view>\n        <view class=\"module-arrow\">></view>\n      </view>\n    </view>\n    \n    <!-- 学习统计 -->\n    <view class=\"stats-section\">\n      <text class=\"stats-title\">今日学习统计</text>\n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <text class=\"stats-number\">{{ todayPracticeCount }}</text>\n          <text class=\"stats-label\">练习次数</text>\n        </view>\n        <view class=\"stats-card\">\n          <text class=\"stats-number\">{{ todayCorrectRate }}%</text>\n          <text class=\"stats-label\">正确率</text>\n        </view>\n        <view class=\"stats-card\">\n          <text class=\"stats-number\">{{ todayStudyTime }}</text>\n          <text class=\"stats-label\">学习时长(分钟)</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue';\nimport { useUserStore } from '../../src/stores/modules/user';\nimport { getPracticeStats } from '../../src/api/modules/study';\n\nconst userStore = useUserStore();\n\n// 响应式数据\nconst todayPracticeCount = ref(0);\nconst todayCorrectRate = ref(0);\nconst todayStudyTime = ref(0);\nconst remainingPracticeCount = ref(3);\n\nonMounted(() => {\n  loadStudyStats();\n});\n\n/**\n * 加载学习统计数据\n */\nasync function loadStudyStats() {\n  try {\n    const stats = await getPracticeStats();\n    todayPracticeCount.value = stats.todayPracticeCount || 0;\n    todayCorrectRate.value = stats.todayCorrectRate || 0;\n    todayStudyTime.value = stats.todayStudyTime || 0;\n    remainingPracticeCount.value = stats.remainingPracticeCount || 3;\n  } catch (error) {\n    uni.__f__('error','at pages/study/study.vue:81','加载学习统计失败:', error);\n  }\n}\n\n/**\n * 获取练习状态文本\n */\nfunction getPracticeStatus() {\n  if (remainingPracticeCount.value > 0) {\n    return `今日还可练习 ${remainingPracticeCount.value} 组`;\n  }\n  return '今日练习次数已用完';\n}\n\n/**\n * 教材学习点击处理\n */\nfunction handleTextbookClick() {\n  uni.showToast({\n    title: '功能建设中，敬请期待',\n    icon: 'none',\n  });\n}\n\n/**\n * 跳转到题库练习\n */\nfunction goToQuestionBank() {\n  uni.navigateTo({ url: '/pages/study/question-bank' });\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\n.study-container {\n  min-height: 100vh;\n  background-color: $background-color;\n  padding: $spacing-md;\n}\n\n.study-header {\n  text-align: center;\n  margin-bottom: $spacing-xl;\n  \n  .page-title {\n    display: block;\n    font-size: $font-size-xxl;\n    font-weight: $font-weight-bold;\n    color: $text-primary;\n    margin-bottom: $spacing-xs;\n  }\n  \n  .page-desc {\n    font-size: $font-size-md;\n    color: $text-secondary;\n  }\n}\n\n.study-modules {\n  margin-bottom: $spacing-xl;\n  \n  .module-card {\n    background-color: $surface-color;\n    border-radius: $border-radius-medium;\n    padding: $spacing-lg;\n    margin-bottom: $spacing-md;\n    display: flex;\n    align-items: center;\n    box-shadow: $shadow-light;\n    \n    .module-icon {\n      font-size: 60rpx;\n      margin-right: $spacing-md;\n    }\n    \n    .module-info {\n      flex: 1;\n      \n      .module-title {\n        display: block;\n        font-size: $font-size-lg;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-xs;\n      }\n      \n      .module-desc {\n        display: block;\n        font-size: $font-size-sm;\n        color: $text-secondary;\n        margin-bottom: $spacing-xs;\n      }\n      \n      .module-status {\n        font-size: $font-size-xs;\n        color: $primary-color;\n      }\n    }\n    \n    .module-arrow {\n      font-size: $font-size-lg;\n      color: $text-disabled;\n    }\n  }\n}\n\n.stats-section {\n  .stats-title {\n    display: block;\n    font-size: $font-size-lg;\n    font-weight: $font-weight-medium;\n    color: $text-primary;\n    margin-bottom: $spacing-md;\n  }\n  \n  .stats-cards {\n    display: flex;\n    gap: $spacing-md;\n    \n    .stats-card {\n      flex: 1;\n      background-color: $surface-color;\n      border-radius: $border-radius-medium;\n      padding: $spacing-lg;\n      text-align: center;\n      box-shadow: $shadow-light;\n      \n      .stats-number {\n        display: block;\n        font-size: $font-size-xl;\n        font-weight: $font-weight-bold;\n        color: $primary-color;\n        margin-bottom: $spacing-xs;\n      }\n      \n      .stats-label {\n        font-size: $font-size-sm;\n        color: $text-secondary;\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/study/study.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "getPracticeStats", "uni"], "mappings": ";;;;;;;AAyDkBA,yCAAa;AAGzB,UAAA,qBAAqBC,kBAAI,CAAC;AAC1B,UAAA,mBAAmBA,kBAAI,CAAC;AACxB,UAAA,iBAAiBA,kBAAI,CAAC;AACtB,UAAA,yBAAyBA,kBAAI,CAAC;AAEpCC,kBAAAA,UAAU,MAAM;AACC;IAAA,CAChB;AAKD,mBAAe,iBAAiB;AAC1B,UAAA;AACI,cAAA,QAAQ,MAAMC,sBAAAA;AACD,2BAAA,QAAQ,MAAM,sBAAsB;AACtC,yBAAA,QAAQ,MAAM,oBAAoB;AACpC,uBAAA,QAAQ,MAAM,kBAAkB;AACxB,+BAAA,QAAQ,MAAM,0BAA0B;AAAA,eACxD,OAAO;AACdC,sBAAA,MAAI,MAAM,SAAQ,+BAA8B,aAAa,KAAK;AAAA,MACpE;AAAA,IACF;AAKA,aAAS,oBAAoB;AACvB,UAAA,uBAAuB,QAAQ,GAAG;AAC7B,eAAA,UAAU,uBAAuB,KAAK;AAAA,MAC/C;AACO,aAAA;AAAA,IACT;AAKA,aAAS,sBAAsB;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AAKA,aAAS,mBAAmB;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,KAAK,6BAA8B,CAAA;AAAA,IACtD;;;;;;;;;;;;;;AC5GA,GAAG,WAAW,eAAe;"}