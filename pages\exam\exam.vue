<template>
  <view class="exam-container">
    <!-- 非正式用户提示 -->
    <view v-if="!userStore.isApproved" class="access-denied">
      <view class="denied-content">
        <text class="denied-icon">🚫</text>
        <text class="denied-title">未认证，无法考试</text>
        <text class="denied-desc">请先完善个人资料并通过机构审核</text>
        <button class="denied-btn" @tap="goToProfile">去认证</button>
      </view>
    </view>
    
    <!-- 正式用户考试内容 -->
    <view v-else class="exam-content">
      <!-- 本期考试 -->
      <view class="exam-section">
        <view class="section-header">
          <text class="section-title">📋 本期考试</text>
        </view>
        
        <view v-if="currentExams.length === 0" class="empty-state">
          <text class="empty-icon">📝</text>
          <text class="empty-text">暂无待考试项</text>
        </view>
        
        <view v-else class="exam-list">
          <view 
            v-for="exam in currentExams" 
            :key="exam.id"
            class="exam-card"
            @tap="handleExamClick(exam)"
          >
            <view class="exam-header">
              <text class="exam-name">{{ exam.name }}</text>
              <view class="exam-type" :class="exam.type">
                {{ exam.type === 'online' ? '线上' : '线下' }}
              </view>
            </view>
            
            <view class="exam-info">
              <text class="exam-time">
                考试时间: {{ formatExamTime(exam.startTime, exam.endTime) }}
              </text>
              <text class="exam-duration">考试时长: {{ exam.duration }}分钟</text>
              <text class="exam-questions">题目数量: {{ exam.totalQuestions }}题</text>
            </view>
            
            <view class="exam-status">
              <text class="status-text" :class="exam.status">
                {{ getStatusText(exam.status) }}
              </text>
              <button 
                v-if="canTakeExam(exam)" 
                class="exam-btn"
                :class="exam.type"
              >
                {{ getActionText(exam) }}
              </button>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 历史考试记录 -->
      <view class="history-section">
        <view class="section-header" @tap="goToHistory">
          <text class="section-title">📊 历史考试记录</text>
          <text class="section-more">查看全部</text>
        </view>
        
        <view v-if="recentHistory.length === 0" class="empty-state">
          <text class="empty-text">暂无考试记录</text>
        </view>
        
        <view v-else class="history-list">
          <view 
            v-for="record in recentHistory" 
            :key="record.id"
            class="history-item"
          >
            <view class="history-info">
              <text class="history-name">{{ record.name }}</text>
              <text class="history-time">{{ formatTime(record.completedTime) }}</text>
            </view>
            <view class="history-result">
              <text class="history-score" :class="record.status">
                {{ record.score || '--' }}分
              </text>
              <text class="history-status" :class="record.status">
                {{ getStatusText(record.status) }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useUserStore } from '../../src/stores/modules/user';
import { storeToRefs } from 'pinia';
import { getCurrentExams, getExamHistory } from '../../src/api/modules/exam';
import type { ExamItem } from '../../src/types/api';

const userStore = useUserStore();
const { isApproved } = storeToRefs(userStore);

// 响应式数据
const currentExams = ref<ExamItem[]>([]);
const recentHistory = ref<any[]>([]);

onMounted(() => {
  if (userStore.isApproved) {
    loadExamData();
  }
});

/**
 * 加载考试数据
 */
async function loadExamData() {
  try {
    const [exams, history] = await Promise.all([
      getCurrentExams(),
      getExamHistory(1, 5),
    ]);
    
    currentExams.value = exams;
    recentHistory.value = history.list || [];
  } catch (error) {
    console.error('加载考试数据失败:', error);
  }
}

/**
 * 考试卡片点击处理
 */
function handleExamClick(exam: ExamItem) {
  if (exam.type === 'online') {
    if (exam.status === 'not_started') {
      uni.navigateTo({ url: `/pages/exam/online-exam?id=${exam.id}` });
    }
  } else {
    uni.navigateTo({ url: `/pages/exam/offline-exam?id=${exam.id}` });
  }
}

/**
 * 判断是否可以参加考试
 */
function canTakeExam(exam: ExamItem) {
  return ['not_started', 'in_progress'].includes(exam.status);
}

/**
 * 获取操作按钮文本
 */
function getActionText(exam: ExamItem) {
  if (exam.type === 'online') {
    return exam.status === 'not_started' ? '开始考试' : '继续考试';
  } else {
    return '立即报名';
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    not_started: '未开始',
    in_progress: '进行中',
    completed: '已完成',
    passed: '已通过',
    failed: '未通过',
    expired: '已过期',
  };
  return statusMap[status] || status;
}

/**
 * 格式化考试时间
 */
function formatExamTime(startTime: string, endTime: string) {
  const start = new Date(startTime);
  const end = new Date(endTime);
  return `${start.toLocaleDateString()} ${start.toLocaleTimeString()} - ${end.toLocaleTimeString()}`;
}

/**
 * 格式化时间
 */
function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleDateString();
}

/**
 * 跳转到个人中心
 */
function goToProfile() {
  uni.switchTab({ url: '/pages/profile/profile' });
}

/**
 * 跳转到历史记录
 */
function goToHistory() {
  uni.navigateTo({ url: '/pages/exam/history' });
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.exam-container {
  min-height: 100vh;
  background-color: $background-color;
}

.access-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: $spacing-xl;
  
  .denied-content {
    text-align: center;
    
    .denied-icon {
      font-size: 120rpx;
      margin-bottom: $spacing-lg;
    }
    
    .denied-title {
      display: block;
      font-size: $font-size-xl;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-sm;
    }
    
    .denied-desc {
      display: block;
      font-size: $font-size-md;
      color: $text-secondary;
      margin-bottom: $spacing-xl;
    }
    
    .denied-btn {
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: $border-radius-medium;
      padding: $spacing-sm $spacing-lg;
      font-size: $font-size-md;
    }
  }
}

.exam-content {
  padding: $spacing-md;
  
  .exam-section, .history-section {
    margin-bottom: $spacing-xl;
    
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-md;
      
      .section-title {
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
      }
      
      .section-more {
        font-size: $font-size-sm;
        color: $primary-color;
      }
    }
    
    .empty-state {
      text-align: center;
      padding: $spacing-xl;
      
      .empty-icon {
        display: block;
        font-size: 80rpx;
        margin-bottom: $spacing-md;
      }
      
      .empty-text {
        font-size: $font-size-md;
        color: $text-disabled;
      }
    }
  }
  
  .exam-list {
    .exam-card {
      background-color: $surface-color;
      border-radius: $border-radius-medium;
      padding: $spacing-lg;
      margin-bottom: $spacing-md;
      box-shadow: $shadow-light;
      
      .exam-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: $spacing-md;
        
        .exam-name {
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          color: $text-primary;
        }
        
        .exam-type {
          padding: 4rpx 12rpx;
          border-radius: $border-radius-small;
          font-size: $font-size-xs;
          color: white;
          
          &.online {
            background-color: $primary-color;
          }
          
          &.offline {
            background-color: $secondary-color;
          }
        }
      }
      
      .exam-info {
        margin-bottom: $spacing-md;
        
        text {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-xs;
        }
      }
      
      .exam-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .status-text {
          font-size: $font-size-sm;
          
          &.not_started {
            color: $warning-color;
          }
          
          &.passed {
            color: $success-color;
          }
          
          &.failed {
            color: $error-color;
          }
          
          &.expired {
            color: $text-disabled;
          }
        }
        
        .exam-btn {
          border: none;
          border-radius: $border-radius-small;
          padding: $spacing-xs $spacing-sm;
          font-size: $font-size-sm;
          color: white;
          
          &.online {
            background-color: $primary-color;
          }
          
          &.offline {
            background-color: $secondary-color;
          }
        }
      }
    }
  }
  
  .history-list {
    background-color: $surface-color;
    border-radius: $border-radius-medium;
    overflow: hidden;
    box-shadow: $shadow-light;
    
    .history-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-md;
      border-bottom: 1rpx solid $divider-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      .history-info {
        .history-name {
          display: block;
          font-size: $font-size-md;
          color: $text-primary;
          margin-bottom: $spacing-xs;
        }
        
        .history-time {
          font-size: $font-size-xs;
          color: $text-disabled;
        }
      }
      
      .history-result {
        text-align: right;
        
        .history-score {
          display: block;
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          margin-bottom: $spacing-xs;
          
          &.passed {
            color: $success-color;
          }
          
          &.failed {
            color: $error-color;
          }
        }
        
        .history-status {
          font-size: $font-size-xs;
          
          &.passed {
            color: $success-color;
          }
          
          &.failed {
            color: $error-color;
          }
        }
      }
    }
  }
}
</style>
