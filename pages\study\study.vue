<template>
  <view class="study-container">
    <view class="study-header">
      <text class="page-title">学习中心</text>
      <text class="page-desc">随时随地，高效学习</text>
    </view>
    
    <view class="study-modules">
      <!-- 教材学习模块 -->
      <view class="module-card" @tap="handleTextbookClick">
        <view class="module-icon">📚</view>
        <view class="module-info">
          <text class="module-title">教材学习</text>
          <text class="module-desc">系统化学习专业教材</text>
          <text class="module-status">功能建设中</text>
        </view>
        <view class="module-arrow">></view>
      </view>
      
      <!-- 题库练习模块 -->
      <view class="module-card" @tap="goToQuestionBank">
        <view class="module-icon">📝</view>
        <view class="module-info">
          <text class="module-title">题库练习</text>
          <text class="module-desc">分类刷题，巩固知识</text>
          <text class="module-status">{{ getPracticeStatus() }}</text>
        </view>
        <view class="module-arrow">></view>
      </view>
    </view>
    
    <!-- 学习统计 -->
    <view class="stats-section">
      <text class="stats-title">今日学习统计</text>
      <view class="stats-cards">
        <view class="stats-card">
          <text class="stats-number">{{ todayPracticeCount }}</text>
          <text class="stats-label">练习次数</text>
        </view>
        <view class="stats-card">
          <text class="stats-number">{{ todayCorrectRate }}%</text>
          <text class="stats-label">正确率</text>
        </view>
        <view class="stats-card">
          <text class="stats-number">{{ todayStudyTime }}</text>
          <text class="stats-label">学习时长(分钟)</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useUserStore } from '../../src/stores/modules/user';
import { getPracticeStats } from '../../src/api/modules/study';

const userStore = useUserStore();

// 响应式数据
const todayPracticeCount = ref(0);
const todayCorrectRate = ref(0);
const todayStudyTime = ref(0);
const remainingPracticeCount = ref(3);

onMounted(() => {
  loadStudyStats();
});

/**
 * 加载学习统计数据
 */
async function loadStudyStats() {
  try {
    const stats = await getPracticeStats();
    todayPracticeCount.value = stats.todayPracticeCount || 0;
    todayCorrectRate.value = stats.todayCorrectRate || 0;
    todayStudyTime.value = stats.todayStudyTime || 0;
    remainingPracticeCount.value = stats.remainingPracticeCount || 3;
  } catch (error) {
    console.error('加载学习统计失败:', error);
  }
}

/**
 * 获取练习状态文本
 */
function getPracticeStatus() {
  if (remainingPracticeCount.value > 0) {
    return `今日还可练习 ${remainingPracticeCount.value} 组`;
  }
  return '今日练习次数已用完';
}

/**
 * 教材学习点击处理
 */
function handleTextbookClick() {
  uni.showToast({
    title: '功能建设中，敬请期待',
    icon: 'none',
  });
}

/**
 * 跳转到题库练习
 */
function goToQuestionBank() {
  uni.navigateTo({ url: '/pages/study/question-bank' });
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.study-container {
  min-height: 100vh;
  background-color: $background-color;
  padding: $spacing-md;
}

.study-header {
  text-align: center;
  margin-bottom: $spacing-xl;
  
  .page-title {
    display: block;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-xs;
  }
  
  .page-desc {
    font-size: $font-size-md;
    color: $text-secondary;
  }
}

.study-modules {
  margin-bottom: $spacing-xl;
  
  .module-card {
    background-color: $surface-color;
    border-radius: $border-radius-medium;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    display: flex;
    align-items: center;
    box-shadow: $shadow-light;
    
    .module-icon {
      font-size: 60rpx;
      margin-right: $spacing-md;
    }
    
    .module-info {
      flex: 1;
      
      .module-title {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }
      
      .module-desc {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-xs;
      }
      
      .module-status {
        font-size: $font-size-xs;
        color: $primary-color;
      }
    }
    
    .module-arrow {
      font-size: $font-size-lg;
      color: $text-disabled;
    }
  }
}

.stats-section {
  .stats-title {
    display: block;
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: $spacing-md;
  }
  
  .stats-cards {
    display: flex;
    gap: $spacing-md;
    
    .stats-card {
      flex: 1;
      background-color: $surface-color;
      border-radius: $border-radius-medium;
      padding: $spacing-lg;
      text-align: center;
      box-shadow: $shadow-light;
      
      .stats-number {
        display: block;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: $primary-color;
        margin-bottom: $spacing-xs;
      }
      
      .stats-label {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}
</style>
