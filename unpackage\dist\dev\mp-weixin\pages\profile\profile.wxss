/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
.profile-container.data-v-dd383ca2 {
  min-height: 100vh;
  background-color: #fafafa;
}
.profile-header.data-v-dd383ca2 {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  padding: 48rpx 32rpx;
  display: flex;
  align-items: center;
}
.profile-header .avatar.data-v-dd383ca2 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.profile-header .user-info.data-v-dd383ca2 {
  flex: 1;
}
.profile-header .user-info .nickname.data-v-dd383ca2 {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 16rpx;
}
.profile-header .user-info .status-badge.data-v-dd383ca2 {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: white;
}
.profile-header .user-info .status-badge.approved.data-v-dd383ca2 {
  background-color: #4caf50;
}
.profile-header .user-info .status-badge.pending.data-v-dd383ca2 {
  background-color: #ff9800;
}
.profile-header .user-info .status-badge.rejected.data-v-dd383ca2 {
  background-color: #f44336;
}
.profile-header .user-info .status-badge.incomplete.data-v-dd383ca2 {
  background-color: #bdbdbd;
}
.menu-section.data-v-dd383ca2 {
  padding: 24rpx;
}
.menu-section .menu-group.data-v-dd383ca2 {
  background-color: #ffffff;
  border-radius: 8rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.menu-section .menu-group .menu-item.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #e0e0e0;
}
.menu-section .menu-group .menu-item.data-v-dd383ca2:last-child {
  border-bottom: none;
}
.menu-section .menu-group .menu-item.logout-item .menu-title.data-v-dd383ca2 {
  color: #f44336;
}
.menu-section .menu-group .menu-item .menu-icon.data-v-dd383ca2 {
  font-size: 36rpx;
  margin-right: 24rpx;
}
.menu-section .menu-group .menu-item .menu-title.data-v-dd383ca2 {
  flex: 1;
  font-size: 28rpx;
  color: #212121;
}
.menu-section .menu-group .menu-item .menu-arrow.data-v-dd383ca2 {
  font-size: 32rpx;
  color: #bdbdbd;
}
.version-info.data-v-dd383ca2 {
  text-align: center;
  padding: 32rpx;
}
.version-info .version-text.data-v-dd383ca2 {
  font-size: 24rpx;
  color: #bdbdbd;
}