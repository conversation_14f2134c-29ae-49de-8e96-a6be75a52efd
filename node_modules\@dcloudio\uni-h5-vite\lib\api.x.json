["$emit", "$off", "$on", "$once", "addInterceptor", "addPhoneContact", "arrayBufferToBase64", "base64ToArrayBuffer", "canIUse", "canvasGetImageData", "canvasPutImageData", "canvasToTempFilePath", "chooseFile", "chooseImage", "chooseLocation", "chooseVideo", "clearStorage", "clearStorageSync", "closeDialogPage", "closePreviewImage", "closeSocket", "connectSocket", "createAnimation", "createCameraContext", "createCanvasContext", "createCanvasContextAsync", "createInnerAudioContext", "createIntersectionObserver", "createLivePlayerContext", "createMapContext", "createMediaQueryObserver", "createSelectorQuery", "createVideoContext", "downloadFile", "getAppBaseInfo", "getClipboardData", "getDeviceInfo", "getElementById", "getEnterOptionsSync", "getFileInfo", "getImageInfo", "getLaunchOptionsSync", "getLeftWindowStyle", "getLocale", "getLocation", "getNetworkType", "get<PERSON><PERSON><PERSON>", "getPushClientId", "get<PERSON><PERSON><PERSON><PERSON>", "getRecorderManager", "getRightWindowStyle", "getSavedFileInfo", "getSavedFileList", "getScreenBrightness", "getSelectedTextRange", "getStorage", "getStorageInfo", "getStorageInfoSync", "getStorageSync", "getSystemInfo", "getSystemInfoSync", "getTabBarPageId", "getTopWindowStyle", "getVideoInfo", "getWindowInfo", "hideActionSheet", "hideKeyboard", "hideLeftWindow", "hideLoading", "hideModal", "hideNavigationBarLoading", "hideRightWindow", "hideTabBar", "hideTabBarRedDot", "hideToast", "hideTopWindow", "interceptors", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadFontFace", "login", "makePhoneCall", "navigateBack", "navigateTo", "offAccelerometerChange", "offAppHide", "offAppShow", "offCompassChange", "offError", "offLocationChange", "offLocationChangeError", "offNetworkStatusChange", "offPageNotFound", "offPushMessage", "offThemeChange", "offUnhandledRejection", "offWindowResize", "onAccelerometerChange", "onAppHide", "onAppShow", "onCompassChange", "onCreateVueApp", "onError", "onGyroscopeChange", "onLocaleChange", "onLocationChange", "onLocationChangeError", "onMemory<PERSON><PERSON>ning", "onNetworkStatusChange", "onPageNotFound", "onPushMessage", "onSocketClose", "onSocketError", "onSocketMessage", "onSocketOpen", "onTabBarMidButtonTap", "onThemeChange", "onUnhandledRejection", "onUserCaptureScreen", "onWindowResize", "openDialogPage", "openDocument", "openLocation", "pageScrollTo", "preloadPage", "previewImage", "reLaunch", "redirectTo", "removeAllPages", "removeInterceptor", "removeLastPage", "removeNonTabBarPages", "removeSavedFile", "removeStorage", "removeStorageSync", "removeTabBarBadge", "request", "rpx2px", "saveFile", "saveImageToPhotosAlbum", "saveVideoToPhotosAlbum", "scanCode", "sendSocketMessage", "setClipboardData", "setKeepScreenOn", "setLeftWindowStyle", "setLocale", "setNavigationBarColor", "setNavigationBarTitle", "setPageMeta", "setRightWindowStyle", "setScreenBrightness", "setStorage", "setStorageSync", "setTabBarBadge", "setTabBarItem", "setTabBarStyle", "setTopWindowStyle", "showActionSheet", "showLeftWindow", "showLoading", "showModal", "showNavigationBarLoading", "showRightWindow", "showTabBar", "showTabBarRedDot", "showToast", "showTopWindow", "startAccelerometer", "startCompass", "startGyroscope", "startLocationUpdate", "startPullDownRefresh", "stopAccelerometer", "stopCompass", "stopGyroscope", "stopLocationUpdate", "stopPullDownRefresh", "switchTab", "uploadFile", "upx2px", "vibrateLong", "vibrateShort"]