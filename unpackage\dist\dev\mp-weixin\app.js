"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const src_stores_modules_user = require("./src/stores/modules/user.js");
const src_stores_modules_app = require("./src/stores/modules/app.js");
const src_stores_index = require("./src/stores/index.js");
if (!Math) {
  "./pages/login/login.js";
  "./pages/register/register.js";
  "./pages/info/info.js";
  "./pages/study/study.js";
  "./pages/exam/exam.js";
  "./pages/profile/profile.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    const appStore = src_stores_modules_app.useAppStore();
    common_vendor.onLaunch(() => {
      common_vendor.index.__f__("log", "at App.vue:10", "App Launch");
      userStore.initProfile();
      appStore.initSystemInfo();
      checkForUpdate();
    });
    common_vendor.onShow(() => {
      common_vendor.index.__f__("log", "at App.vue:23", "App Show");
    });
    common_vendor.onHide(() => {
      common_vendor.index.__f__("log", "at App.vue:27", "App Hide");
    });
    function checkForUpdate() {
      const updateManager = common_vendor.index.getUpdateManager();
      updateManager.onCheckForUpdate((res) => {
        common_vendor.index.__f__("log", "at App.vue:38", "检查更新结果:", res.hasUpdate);
      });
      updateManager.onUpdateReady(() => {
        common_vendor.index.showModal({
          title: "更新提示",
          content: "新版本已经准备好，是否重启应用？",
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      updateManager.onUpdateFailed(() => {
        common_vendor.index.showToast({
          title: "更新失败，请稍后重试",
          icon: "none"
        });
      });
    }
    return () => {
    };
  }
});
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(src_stores_index.pinia);
  return {
    app,
    Pinia: src_stores_index.pinia
    // 如果 nvue 使用 pinia，需要这个
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
