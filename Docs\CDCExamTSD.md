# 微信小程序开发技术约定文档 (v1.4)

> **版本**: 1.4
>
> **更新时间**: 2025-01-27
>
> **适用范围**: 所有基于 uniapp + Vue 3 + TypeScript 的微信小程序项目

## 📋 目录

- [1. 技术栈选型](#1-技术栈选型)
- [2. 项目结构规范](#2-项目结构规范)
- [3. 编码规范](#3-编码规范)
- [4. 组件开发约定](#4-组件开发约定)
- [5. TypeScript 规范](#5-typescript-规范)
- [6. 状态管理规范 (Pinia)](#6-状态管理规范-pinia)
- [7. HTTP 请求规范](#7-http-请求规范)
- [8. 包体积与性能优化](#8-包体积与性能优化)
- [9. Git 工作流与提交规范](#9-git-工作流与提交规范)
- [10. 小程序环境特殊约定](#10-小程序环境特殊约定)
- [11. 强制执行规则](#11-强制执行规则)

---

## 1. 技术栈选型

### 1.1 核心技术栈 (强制)

| 技术分类      | 选型                      | 版本要求   |
| :------------ | :------------------------ | :--------- |
| **跨端框架** | uniapp                    | ≥ 3.0.0    |
| **前端框架** | Vue 3 (Composition API)   | ≥ 3.3.0    |
| **编程语言** | TypeScript                | ≥ 4.9.0    |
| **UI 组件库** | **uview-plus** | ≥ 3.1.0    |
| **状态管理** | **Pinia** | ≥ 2.1.0    |
| **HTTP 请求库** | **luch-request** | ≥ 3.1.0    |

### 1.2 选择理由

- **uview-plus**: Vue 3 兼容版本，组件丰富，**专为 uniapp 深度优化**，社区活跃，是Vue3项目的首选。
- **Pinia**: Vue 3 官方推荐，API 简洁，类型推导完美，符合组合式函数心智模型。
- **luch-request**: uniapp 生态企业级请求库，**支持全端平台**，拦截器等功能强大且稳定。

---

## 2. 项目结构规范

### 2.1 目录结构 (强制)

```
src/
├── api/                    # API 接口封装
├── components/             # 全局公共组件 (符合 easycom 规范)
│   ├── common/             # 通用原子组件 (如 MyButton)
│   └── business/           # 业务区块组件 (如 ProductCard)
├── composables/            # Vue 组合式函数 (逻辑复用)
├── pages/                  # 主包页面
├── static/                 # 静态资源 (图片/字体等，优先使用CDN)
├── stores/                 # Pinia 状态管理
│   └── modules/            # 模块化 store
├── styles/                 # 全局样式与变量
├── subpackages/            # 分包页面
├── types/                  # 全局 TypeScript 类型定义
│   └── api.d.ts            # API 相关类型
└── utils/                  # 工具函数
├── App.vue                 # 应用根组件
├── main.ts                 # 主入口文件
├── manifest.json           # 应用清单文件
├── pages.json              # 页面路由与 easycom 配置
└── tsconfig.json           # TypeScript 配置文件
```

### 2.2 命名规范 (强制)

- **目录**: `kebab-case` (短横线连接) 或 `小写单词` (如: `subpackages`, `user-center`)
- **页面文件**: `kebab-case` (如: `order-list.vue`)
- **组件文件**: `PascalCase` (大驼峰) (如: `UserProfile.vue`)
- **工具/类型/接口文件**: `camelCase` (小驼峰) (如: `httpUtils.ts`, `apiTypes.ts`)

---

## 3. 编码规范

### 3.1 代码风格 (强制)

**强制使用 ESLint + Prettier 组合**。ESLint 负责代码质量，Prettier 负责代码格式。

```javascript
// .eslintrc.js 示例 (只负责代码质量)
module.exports = {
  root: true,
  env: { 'vue/setup-compiler-macros': true },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    'plugin:vue/vue3-recommended',
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'max-len': ['error', { code: 120, ignoreUrls: true }],
    '@typescript-eslint/no-explicit-any': 'warn', // 警告 any 类型，鼓励明确类型
  },
};
```json
// .prettierrc.json 示例 (只负责代码格式)
{
  "semi": true,
  "singleQuote": true,
  "trailingComma": "all",
  "printWidth": 120,
  "tabWidth": 2,
  "arrowParens": "always"
}
```

### 3.2 文件与函数长度 (强制)

- 单个文件 (`.vue`, `.ts`) **逻辑代码不应超过 500 行**。
- 单个函数/方法 **不应超过 50 行**。
- 超出限制时，**必须**通过拆分组件或抽离到 `composables` 等方式进行重构。

---

## 4. 组件开发约定

### 4.1 组件模板 (强制)

所有组件**必须**使用 `<script setup lang="ts">` 语法糖，并遵循以下结构。

```vue
<template>
  <view class="user-profile">
    <!-- 组件内容 -->
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { UserInfo } from '@/types/api.d.ts'; // 导入类型

// 1. Props 定义：必须有类型，可选属性必须有默认值
interface Props {
  /** 用户信息对象 */
  user: UserInfo;
  /** 显示模式 */
  mode?: 'simple' | 'full';
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'full',
});

// 2. Emits 定义：必须使用泛型函数签名定义事件和载荷类型
const emit = defineEmits<{
  (e: 'avatar-click', userId: string): void;
}>();

// 3. 鼓励使用 Computed 计算属性
const displayName = computed(() => props.user.nickname || props.user.username);

// 4. 事件处理函数
function handleAvatarClick() {
  emit('avatar-click', props.user.id);
}
</script>

<style lang="scss" scoped>
.user-profile {
  // 样式隔离
}
</style>
```

---

## 5. TypeScript 规范

### 5.1 全局类型管理 (强制)

**必须**在 `src/types/` 目录下集中管理所有全局类型定义 (如 API 结构)。

```typescript
// src/types/api.d.ts
/** 通用 API 响应结构 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/** 用户信息 */
export interface UserInfo {
  id: string;
  username: string;
  nickname: string;
  avatar?: string;
  token: string;
}
```

### 5.2 禁用 `any` (强制)

**严禁**在项目中使用 `any` 类型。对于不确定的类型，应使用 `unknown` 并进行类型守卫，或使用泛型。

---

## 6. 状态管理规范 (Pinia)

### 6.1 Store 结构 (强制)

**必须**采用 Setup Store 写法，以实现最優的类型推导与组合式能力。

```typescript
// src/stores/modules/user.ts
import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type { UserInfo } from '@/types/api.d.ts';

export const useUserStore = defineStore('user', () => {
  // State
  const profile = ref<UserInfo | null>(null);

  // Getters
  const isLoggedIn = computed(() => !!profile.value?.token);
  const token = computed(() => profile.value?.token || '');

  // Actions
  function setProfile(userInfo: UserInfo) {
    profile.value = userInfo;
    uni.setStorageSync('user_profile', userInfo);
  }

  function clearProfile() {
    profile.value = null;
    uni.removeStorageSync('user_profile');
  }

  return {
    profile,
    isLoggedIn,
    token,
    setProfile,
    clearProfile,
  };
});
```

### 6.2 使用规范 (强制)

为保持 State/Getters 的响应性，解构时**必须**使用 `storeToRefs`。

```vue
<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user';
import { storeToRefs } from 'pinia';

const userStore = useUserStore();
const { profile, isLoggedIn } = storeToRefs(userStore); // 响应式
const { setProfile } = userStore; // action 直接解构
</script>
```

---

## 7. HTTP 请求规范

### 7.1 请求封装 (强制)

使用 `luch-request` 进行统一封装，包含拦截器、错误处理和业务判断。

```typescript
// src/utils/request.ts
import Request from 'luch-request';
import { useUserStore } from '@/stores/modules/user';
import type { ApiResponse } from '@/types/api.d.ts';

const http = new Request({
  baseURL: 'https://api.example.com/v1',
  timeout: 10000,
  header: { 'Content-Type': 'application/json' },
});

// 请求拦截器
http.interceptors.request.use((config) => {
  const userStore = useUserStore();
  if (userStore.isLoggedIn) {
    config.header.Authorization = `Bearer ${userStore.token}`;
  }
  uni.showLoading({ title: '加载中...', mask: true });
  return config;
});

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    uni.hideLoading();
    const res = response.data as ApiResponse;

    // 业务成功 (code: 200)
    if (res.code === 200) {
      return Promise.resolve(res.data);
    }
    
    // Token 失效 (code: 401)
    if (res.code === 401) {
        useUserStore().clearProfile();
        // 重定向到登录页
    }

    // 其他业务错误
    uni.showToast({ title: res.message || 'Error', icon: 'none' });
    return Promise.reject(res);
  },
  (error) => {
    uni.hideLoading();
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    return Promise.reject(error);
  },
);

export default http;
```

---

## 8. 包体积与性能优化

### 8.1 分包策略 (强制)

- **主包 (≤ 2MB)**: 仅包含 TabBar 页面、登录页、核心公共组件和工具。
- **分包 (≤ 2MB/个)**: 按业务模块（订单、用户中心）或使用频率（帮助中心）划分。
- **必须**为核心业务分包配置 `preloadRule` 预加载，优化用户跳转体验。

### 8.2 资源优化 (强制)

- **图片**: 优先使用 CDN。本地图片必须压缩，推荐 `webp` 格式。
- **懒加载**: 非首屏的长列表或大图，**必须**使用 `<image>` 的 `lazy-load` 属性。
- **虚拟列表**: 列表项超过 100 条时，**必须**采用虚拟列表技术方案。

### 8.3 按需引入 (强制)

**必须使用 `easycom` 实现组件的自动按需引入**，禁止手动全量或局部导入 UI 组件。

```json
// pages.json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "u-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^My(.*)": "@/components/common/My$1/My$1.vue"
    }
  }
}
```

---

## 9. Git 工作流与提交规范

### 9.1 分支模型 (强制)

- `main`: 主分支，与线上版本保持一致。
- `develop`: 开发分支，所有功能分支基于此创建。
- `feature/xxx`: 功能分支 (如: `feature/user-login`)。
- `hotfix/xxx`: 紧急线上 bug 修复分支。

### 9.2 提交规范 (强制)

**必须**遵循 **Conventional Commits** 规范，推荐使用 `commitizen` 工具。

- **格式**: `<type>(<scope>): <subject>`
- **类型 `type`**: `feat`, `fix`, `refactor`, `perf`, `style`, `docs`, `chore`, `test`。
- **示例**: `feat(user): add user profile page`

---

## 10. 小程序环境特殊约定

- **版本更新**: **必须**在 `App.vue` 的 `onLaunch` 中调用 `wx.getUpdateManager`，处理小程序的强制更新逻辑。
- **登录流程**: **必须**遵循 `uni.login` 获取 `code` -> 发送给后端换取 `token` 的标准模式。
- **API 限制**: **严禁**使用 `window`, `document`, `localStorage` 等 Web-Only API。数据持久化使用 `uni.setStorageSync`，但需注意其 10MB 体积限制和同步执行特性。
- **页面导航**: 注意 `uni.navigateTo` 有 10 层页面栈限制，在适当场景下（如登录后跳转首页）**必须**使用 `uni.reLaunch` 或 `uni.redirectTo` 清理页面栈。
- **用户授权**: 获取用户昵称头像等敏感信息，**必须**遵循微信最新的隐私指引，通过用户主动点击按钮等方式触发授权，不得静默获取。

---

## 11. 强制执行规则

1.  **代码必须通过 ESLint + Prettier 校验**，不符合规范的 MR 将被拒绝。
2.  **严禁使用 `any` 类型**，所有核心数据必须有 TypeScript 类型定义。
3.  **组件必须使用 Composition API (`<script setup>`)**。
4.  **状态管理必须使用 Pinia Setup Store 模式**。
5.  **HTTP 请求必须通过统一封装的 `request` 实例发起**。
6.  **包体积必须符合限制要求**。
7.  **Git 提交信息必须符合约定规范**。

> 本文档为团队开发的核心基石，所有成员必须严格遵守。持续学习，共同维护高质量的代码库。