"use strict";
const common_vendor = require("../../../common/vendor.js");
const useAppStore = common_vendor.defineStore("app", () => {
  const loading = common_vendor.ref(false);
  const currentTabIndex = common_vendor.ref(0);
  const systemInfo = common_vendor.ref(null);
  const isLoading = common_vendor.computed(() => loading.value);
  const statusBarHeight = common_vendor.computed(() => {
    var _a;
    return ((_a = systemInfo.value) == null ? void 0 : _a.statusBarHeight) || 0;
  });
  const screenHeight = common_vendor.computed(() => {
    var _a;
    return ((_a = systemInfo.value) == null ? void 0 : _a.screenHeight) || 0;
  });
  const screenWidth = common_vendor.computed(() => {
    var _a;
    return ((_a = systemInfo.value) == null ? void 0 : _a.screenWidth) || 0;
  });
  function setLoading(status) {
    loading.value = status;
  }
  function setCurrentTab(index) {
    currentTabIndex.value = index;
  }
  function initSystemInfo() {
    common_vendor.index.getSystemInfo({
      success: (res) => {
        systemInfo.value = res;
      },
      fail: (error) => {
        common_vendor.index.__f__("error", "at src/stores/modules/app.ts:34", "获取系统信息失败:", error);
      }
    });
  }
  return {
    // State
    loading,
    currentTabIndex,
    systemInfo,
    // Getters
    isLoading,
    statusBarHeight,
    screenHeight,
    screenWidth,
    // Actions
    setLoading,
    setCurrentTab,
    initSystemInfo
  };
});
exports.useAppStore = useAppStore;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/stores/modules/app.js.map
