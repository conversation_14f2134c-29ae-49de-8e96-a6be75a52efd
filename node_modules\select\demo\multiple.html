<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>multiple</title>
</head>
<body>
    <!-- 1. Define some markup -->
    <button type="button">Select</button>
    <input type="text" value="Lorem ipsum">
    <textarea>Lorem ipsum</textarea>

    <!-- 2. Include library -->
    <script src="../dist/select.js"></script>

    <!-- 3. Select! -->
    <script>
    var input = document.querySelector('input');
    var textarea = document.querySelector('textarea');
    var button = document.querySelector('button');

    button.addEventListener('click', function(e) {
        console.log(select(input));
        console.log(select(textarea));
    });
    </script>
</body>
</html>
