uni-scroll-view {
  display: block;
  width: 100%;
}

uni-scroll-view[hidden] {
  display: none;
}

.uni-scroll-view {
  position: relative;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  /* display: flex; 时在安卓下会导致scrollWidth和offsetWidth一样 */
  height: 100%;
  max-height: inherit;
}

.uni-scroll-view-scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

.uni-scroll-view-scrollbar-hidden {
  -moz-scrollbars: none;
  scrollbar-width: none;
}

.uni-scroll-view-content {
  width: 100%;
  height: 100%;
}