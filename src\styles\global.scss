/**
 * 全局样式
 */
@import './variables.scss';

// ==================== 全局重置 ====================
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}

page {
  background-color: $background-color;
  color: $text-primary;
  font-size: $font-size-md;
  line-height: $line-height-normal;
}

// ==================== 通用布局类 ====================
.container {
  padding: $spacing-md;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

// ==================== 文字样式类 ====================
.text-primary {
  color: $text-primary;
}

.text-secondary {
  color: $text-secondary;
}

.text-disabled {
  color: $text-disabled;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: $font-weight-bold;
}

.font-medium {
  font-weight: $font-weight-medium;
}

// ==================== 间距类 ====================
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-md { margin: $spacing-md; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }

.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }

.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }

.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }

.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }

.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }

.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-md { padding-left: $spacing-md; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }

.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-md { padding-right: $spacing-md; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }

// ==================== 状态样式类 ====================
.status-pending {
  color: $status-pending;
}

.status-approved {
  color: $status-approved;
}

.status-rejected {
  color: $status-rejected;
}

.status-expired {
  color: $status-expired;
}

// ==================== 卡片样式 ====================
.card {
  background-color: $surface-color;
  border-radius: $border-radius-medium;
  box-shadow: $shadow-light;
  padding: $spacing-md;
  margin-bottom: $spacing-md;
}

.card-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin-bottom: $spacing-sm;
}

.card-content {
  color: $text-secondary;
  line-height: $line-height-loose;
}

// ==================== 按钮样式 ====================
.btn-primary {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: $border-radius-medium;
  padding: $spacing-sm $spacing-lg;
  font-size: $font-size-md;
  transition: $transition-fast;
}

.btn-primary:active {
  background-color: $primary-dark;
}

.btn-secondary {
  background-color: $secondary-color;
  color: white;
  border: none;
  border-radius: $border-radius-medium;
  padding: $spacing-sm $spacing-lg;
  font-size: $font-size-md;
  transition: $transition-fast;
}

.btn-secondary:active {
  background-color: $secondary-dark;
}

// ==================== 分割线 ====================
.divider {
  height: 1rpx;
  background-color: $divider-color;
  margin: $spacing-md 0;
}
