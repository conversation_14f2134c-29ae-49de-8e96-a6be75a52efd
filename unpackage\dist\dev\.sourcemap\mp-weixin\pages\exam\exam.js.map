{"version": 3, "file": "exam.js", "sources": ["pages/exam/exam.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZXhhbS9leGFtLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"exam-container\">\n    <!-- 非正式用户提示 -->\n    <view v-if=\"!userStore.isApproved\" class=\"access-denied\">\n      <view class=\"denied-content\">\n        <text class=\"denied-icon\">🚫</text>\n        <text class=\"denied-title\">未认证，无法考试</text>\n        <text class=\"denied-desc\">请先完善个人资料并通过机构审核</text>\n        <button class=\"denied-btn\" @tap=\"goToProfile\">去认证</button>\n      </view>\n    </view>\n    \n    <!-- 正式用户考试内容 -->\n    <view v-else class=\"exam-content\">\n      <!-- 本期考试 -->\n      <view class=\"exam-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">📋 本期考试</text>\n        </view>\n        \n        <view v-if=\"currentExams.length === 0\" class=\"empty-state\">\n          <text class=\"empty-icon\">📝</text>\n          <text class=\"empty-text\">暂无待考试项</text>\n        </view>\n        \n        <view v-else class=\"exam-list\">\n          <view \n            v-for=\"exam in currentExams\" \n            :key=\"exam.id\"\n            class=\"exam-card\"\n            @tap=\"handleExamClick(exam)\"\n          >\n            <view class=\"exam-header\">\n              <text class=\"exam-name\">{{ exam.name }}</text>\n              <view class=\"exam-type\" :class=\"exam.type\">\n                {{ exam.type === 'online' ? '线上' : '线下' }}\n              </view>\n            </view>\n            \n            <view class=\"exam-info\">\n              <text class=\"exam-time\">\n                考试时间: {{ formatExamTime(exam.startTime, exam.endTime) }}\n              </text>\n              <text class=\"exam-duration\">考试时长: {{ exam.duration }}分钟</text>\n              <text class=\"exam-questions\">题目数量: {{ exam.totalQuestions }}题</text>\n            </view>\n            \n            <view class=\"exam-status\">\n              <text class=\"status-text\" :class=\"exam.status\">\n                {{ getStatusText(exam.status) }}\n              </text>\n              <button \n                v-if=\"canTakeExam(exam)\" \n                class=\"exam-btn\"\n                :class=\"exam.type\"\n              >\n                {{ getActionText(exam) }}\n              </button>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 历史考试记录 -->\n      <view class=\"history-section\">\n        <view class=\"section-header\" @tap=\"goToHistory\">\n          <text class=\"section-title\">📊 历史考试记录</text>\n          <text class=\"section-more\">查看全部</text>\n        </view>\n        \n        <view v-if=\"recentHistory.length === 0\" class=\"empty-state\">\n          <text class=\"empty-text\">暂无考试记录</text>\n        </view>\n        \n        <view v-else class=\"history-list\">\n          <view \n            v-for=\"record in recentHistory\" \n            :key=\"record.id\"\n            class=\"history-item\"\n          >\n            <view class=\"history-info\">\n              <text class=\"history-name\">{{ record.name }}</text>\n              <text class=\"history-time\">{{ formatTime(record.completedTime) }}</text>\n            </view>\n            <view class=\"history-result\">\n              <text class=\"history-score\" :class=\"record.status\">\n                {{ record.score || '--' }}分\n              </text>\n              <text class=\"history-status\" :class=\"record.status\">\n                {{ getStatusText(record.status) }}\n              </text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue';\nimport { useUserStore } from '../../src/stores/modules/user';\nimport { storeToRefs } from 'pinia';\nimport { getCurrentExams, getExamHistory } from '../../src/api/modules/exam';\nimport type { ExamItem } from '../../src/types/api';\n\nconst userStore = useUserStore();\nconst { isApproved } = storeToRefs(userStore);\n\n// 响应式数据\nconst currentExams = ref<ExamItem[]>([]);\nconst recentHistory = ref<any[]>([]);\n\nonMounted(() => {\n  if (userStore.isApproved) {\n    loadExamData();\n  }\n});\n\n/**\n * 加载考试数据\n */\nasync function loadExamData() {\n  try {\n    const [exams, history] = await Promise.all([\n      getCurrentExams(),\n      getExamHistory(1, 5),\n    ]);\n    \n    currentExams.value = exams;\n    recentHistory.value = history.list || [];\n  } catch (error) {\n    uni.__f__('error','at pages/exam/exam.vue:133','加载考试数据失败:', error);\n  }\n}\n\n/**\n * 考试卡片点击处理\n */\nfunction handleExamClick(exam: ExamItem) {\n  if (exam.type === 'online') {\n    if (exam.status === 'not_started') {\n      uni.navigateTo({ url: `/pages/exam/online-exam?id=${exam.id}` });\n    }\n  } else {\n    uni.navigateTo({ url: `/pages/exam/offline-exam?id=${exam.id}` });\n  }\n}\n\n/**\n * 判断是否可以参加考试\n */\nfunction canTakeExam(exam: ExamItem) {\n  return ['not_started', 'in_progress'].includes(exam.status);\n}\n\n/**\n * 获取操作按钮文本\n */\nfunction getActionText(exam: ExamItem) {\n  if (exam.type === 'online') {\n    return exam.status === 'not_started' ? '开始考试' : '继续考试';\n  } else {\n    return '立即报名';\n  }\n}\n\n/**\n * 获取状态文本\n */\nfunction getStatusText(status: string) {\n  const statusMap: Record<string, string> = {\n    not_started: '未开始',\n    in_progress: '进行中',\n    completed: '已完成',\n    passed: '已通过',\n    failed: '未通过',\n    expired: '已过期',\n  };\n  return statusMap[status] || status;\n}\n\n/**\n * 格式化考试时间\n */\nfunction formatExamTime(startTime: string, endTime: string) {\n  const start = new Date(startTime);\n  const end = new Date(endTime);\n  return `${start.toLocaleDateString()} ${start.toLocaleTimeString()} - ${end.toLocaleTimeString()}`;\n}\n\n/**\n * 格式化时间\n */\nfunction formatTime(timeStr: string) {\n  return new Date(timeStr).toLocaleDateString();\n}\n\n/**\n * 跳转到个人中心\n */\nfunction goToProfile() {\n  uni.switchTab({ url: '/pages/profile/profile' });\n}\n\n/**\n * 跳转到历史记录\n */\nfunction goToHistory() {\n  uni.navigateTo({ url: '/pages/exam/history' });\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\n.exam-container {\n  min-height: 100vh;\n  background-color: $background-color;\n}\n\n.access-denied {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 60vh;\n  padding: $spacing-xl;\n  \n  .denied-content {\n    text-align: center;\n    \n    .denied-icon {\n      font-size: 120rpx;\n      margin-bottom: $spacing-lg;\n    }\n    \n    .denied-title {\n      display: block;\n      font-size: $font-size-xl;\n      font-weight: $font-weight-medium;\n      color: $text-primary;\n      margin-bottom: $spacing-sm;\n    }\n    \n    .denied-desc {\n      display: block;\n      font-size: $font-size-md;\n      color: $text-secondary;\n      margin-bottom: $spacing-xl;\n    }\n    \n    .denied-btn {\n      background-color: $primary-color;\n      color: white;\n      border: none;\n      border-radius: $border-radius-medium;\n      padding: $spacing-sm $spacing-lg;\n      font-size: $font-size-md;\n    }\n  }\n}\n\n.exam-content {\n  padding: $spacing-md;\n  \n  .exam-section, .history-section {\n    margin-bottom: $spacing-xl;\n    \n    .section-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: $spacing-md;\n      \n      .section-title {\n        font-size: $font-size-lg;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n      }\n      \n      .section-more {\n        font-size: $font-size-sm;\n        color: $primary-color;\n      }\n    }\n    \n    .empty-state {\n      text-align: center;\n      padding: $spacing-xl;\n      \n      .empty-icon {\n        display: block;\n        font-size: 80rpx;\n        margin-bottom: $spacing-md;\n      }\n      \n      .empty-text {\n        font-size: $font-size-md;\n        color: $text-disabled;\n      }\n    }\n  }\n  \n  .exam-list {\n    .exam-card {\n      background-color: $surface-color;\n      border-radius: $border-radius-medium;\n      padding: $spacing-lg;\n      margin-bottom: $spacing-md;\n      box-shadow: $shadow-light;\n      \n      .exam-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-bottom: $spacing-md;\n        \n        .exam-name {\n          font-size: $font-size-lg;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n        }\n        \n        .exam-type {\n          padding: 4rpx 12rpx;\n          border-radius: $border-radius-small;\n          font-size: $font-size-xs;\n          color: white;\n          \n          &.online {\n            background-color: $primary-color;\n          }\n          \n          &.offline {\n            background-color: $secondary-color;\n          }\n        }\n      }\n      \n      .exam-info {\n        margin-bottom: $spacing-md;\n        \n        text {\n          display: block;\n          font-size: $font-size-sm;\n          color: $text-secondary;\n          margin-bottom: $spacing-xs;\n        }\n      }\n      \n      .exam-status {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        \n        .status-text {\n          font-size: $font-size-sm;\n          \n          &.not_started {\n            color: $warning-color;\n          }\n          \n          &.passed {\n            color: $success-color;\n          }\n          \n          &.failed {\n            color: $error-color;\n          }\n          \n          &.expired {\n            color: $text-disabled;\n          }\n        }\n        \n        .exam-btn {\n          border: none;\n          border-radius: $border-radius-small;\n          padding: $spacing-xs $spacing-sm;\n          font-size: $font-size-sm;\n          color: white;\n          \n          &.online {\n            background-color: $primary-color;\n          }\n          \n          &.offline {\n            background-color: $secondary-color;\n          }\n        }\n      }\n    }\n  }\n  \n  .history-list {\n    background-color: $surface-color;\n    border-radius: $border-radius-medium;\n    overflow: hidden;\n    box-shadow: $shadow-light;\n    \n    .history-item {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: $spacing-md;\n      border-bottom: 1rpx solid $divider-color;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .history-info {\n        .history-name {\n          display: block;\n          font-size: $font-size-md;\n          color: $text-primary;\n          margin-bottom: $spacing-xs;\n        }\n        \n        .history-time {\n          font-size: $font-size-xs;\n          color: $text-disabled;\n        }\n      }\n      \n      .history-result {\n        text-align: right;\n        \n        .history-score {\n          display: block;\n          font-size: $font-size-md;\n          font-weight: $font-weight-medium;\n          margin-bottom: $spacing-xs;\n          \n          &.passed {\n            color: $success-color;\n          }\n          \n          &.failed {\n            color: $error-color;\n          }\n        }\n        \n        .history-status {\n          font-size: $font-size-xs;\n          \n          &.passed {\n            color: $success-color;\n          }\n          \n          &.failed {\n            color: $error-color;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/exam/exam.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "storeToRefs", "ref", "onMounted", "getCurrentExams", "getExamHistory", "uni"], "mappings": ";;;;;;;AA0GA,UAAM,YAAYA,wBAAAA;AACKC,kBAAAA,YAAY,SAAS;AAGtC,UAAA,eAAeC,kBAAgB,CAAA,CAAE;AACjC,UAAA,gBAAgBA,kBAAW,CAAA,CAAE;AAEnCC,kBAAAA,UAAU,MAAM;AACd,UAAI,UAAU,YAAY;AACX;MACf;AAAA,IAAA,CACD;AAKD,mBAAe,eAAe;AACxB,UAAA;AACF,cAAM,CAAC,OAAO,OAAO,IAAI,MAAM,QAAQ,IAAI;AAAA,UACzCC,qCAAgB;AAAA,UAChBC,qBAAA,eAAe,GAAG,CAAC;AAAA,QAAA,CACpB;AAED,qBAAa,QAAQ;AACP,sBAAA,QAAQ,QAAQ,QAAQ,CAAA;AAAA,eAC/B,OAAO;AACdC,sBAAA,MAAI,MAAM,SAAQ,8BAA6B,aAAa,KAAK;AAAA,MACnE;AAAA,IACF;AAKA,aAAS,gBAAgB,MAAgB;AACnC,UAAA,KAAK,SAAS,UAAU;AACtB,YAAA,KAAK,WAAW,eAAe;AACjCA,8BAAI,WAAW,EAAE,KAAK,8BAA8B,KAAK,EAAE,IAAI;AAAA,QACjE;AAAA,MAAA,OACK;AACLA,4BAAI,WAAW,EAAE,KAAK,+BAA+B,KAAK,EAAE,IAAI;AAAA,MAClE;AAAA,IACF;AAKA,aAAS,YAAY,MAAgB;AACnC,aAAO,CAAC,eAAe,aAAa,EAAE,SAAS,KAAK,MAAM;AAAA,IAC5D;AAKA,aAAS,cAAc,MAAgB;AACjC,UAAA,KAAK,SAAS,UAAU;AACnB,eAAA,KAAK,WAAW,gBAAgB,SAAS;AAAA,MAAA,OAC3C;AACE,eAAA;AAAA,MACT;AAAA,IACF;AAKA,aAAS,cAAc,QAAgB;AACrC,YAAM,YAAoC;AAAA,QACxC,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,MAAA;AAEJ,aAAA,UAAU,MAAM,KAAK;AAAA,IAC9B;AAKS,aAAA,eAAe,WAAmB,SAAiB;AACpD,YAAA,QAAQ,IAAI,KAAK,SAAS;AAC1B,YAAA,MAAM,IAAI,KAAK,OAAO;AACrB,aAAA,GAAG,MAAM,mBAAA,CAAoB,IAAI,MAAM,mBAAA,CAAoB,MAAM,IAAI,mBAAA,CAAoB;AAAA,IAClG;AAKA,aAAS,WAAW,SAAiB;AACnC,aAAO,IAAI,KAAK,OAAO,EAAE,mBAAmB;AAAA,IAC9C;AAKA,aAAS,cAAc;AACrBA,oBAAAA,MAAI,UAAU,EAAE,KAAK,yBAA0B,CAAA;AAAA,IACjD;AAKA,aAAS,cAAc;AACrBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,sBAAuB,CAAA;AAAA,IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjNA,GAAG,WAAW,eAAe;"}