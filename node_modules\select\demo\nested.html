<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>non-editable</title>
</head>
<body>
    <!-- 1. Define some markup -->
    <button type="button">Select</button>
    <div>
        <p>Item 1</p>
        <p>Item 2</p>
        <ul>
            <li>Item 3</li>
            <li>Item 4</li>
            <li>Item 5</li>
        </ul>
    </div>

    <!-- 2. Include library -->
    <script src="../dist/select.js"></script>

    <!-- 3. Select! -->
    <script>
    var div = document.querySelector('div');
    var button = document.querySelector('button');

    button.addEventListener('click', function(e) {
        var selected = select(div);
        console.log(selected);
    });
    </script>
</body>
</html>
