/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
.info-container.data-v-f52d2d81 {
  min-height: 100vh;
  background-color: #fafafa;
}
.status-tip.data-v-f52d2d81 {
  margin: 32rpx;
}
.status-tip .tip-content.data-v-f52d2d81 {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.status-tip .tip-content .tip-icon.data-v-f52d2d81 {
  font-size: 36rpx;
  margin-right: 24rpx;
}
.status-tip .tip-content .tip-text.data-v-f52d2d81 {
  flex: 1;
}
.status-tip .tip-content .tip-text .tip-title.data-v-f52d2d81 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
  margin-bottom: 8rpx;
}
.status-tip .tip-content .tip-text .tip-desc.data-v-f52d2d81 {
  font-size: 24rpx;
  color: #757575;
}
.status-tip .tip-content .tip-btn.data-v-f52d2d81 {
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}
.content-section .banner-section.data-v-f52d2d81 {
  margin: 24rpx;
}
.content-section .banner-section .banner-swiper.data-v-f52d2d81 {
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.content-section .banner-section .banner-swiper .banner-item.data-v-f52d2d81 {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
}
.content-section .banner-section .banner-swiper .banner-item .banner-title.data-v-f52d2d81 {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
}
.content-section .info-list .info-section.data-v-f52d2d81 {
  margin: 24rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.content-section .info-list .info-section .section-header.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #e0e0e0;
}
.content-section .info-list .info-section .section-header .section-title.data-v-f52d2d81 {
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
}
.content-section .info-list .info-section .section-header .section-more.data-v-f52d2d81 {
  font-size: 24rpx;
  color: #1976d2;
}
.content-section .info-list .info-section .info-items .info-item.data-v-f52d2d81 {
  padding: 24rpx;
  border-bottom: 1rpx solid #e0e0e0;
}
.content-section .info-list .info-section .info-items .info-item.data-v-f52d2d81:last-child {
  border-bottom: none;
}
.content-section .info-list .info-section .info-items .info-item .item-header.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.content-section .info-list .info-section .info-items .info-item .item-header .item-title.data-v-f52d2d81 {
  flex: 1;
  font-size: 28rpx;
  color: #212121;
  line-height: 1.4;
}
.content-section .info-list .info-section .info-items .info-item .item-header .top-tag.data-v-f52d2d81 {
  background-color: #f44336;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-left: 16rpx;
}
.content-section .info-list .info-section .info-items .info-item .item-time.data-v-f52d2d81 {
  font-size: 20rpx;
  color: #bdbdbd;
}