uni-textarea {
  width: 300px;
  height: 150px;
  display: block;
  position: relative;
  font-size: 16px;
  line-height: normal;
  white-space: pre-wrap;
  word-break: break-all;
}
uni-textarea[hidden] {
  display: none;
}
uni-textarea[auto-height="true"] {
  height: fit-content !important;
}
.uni-textarea-wrapper,
.uni-textarea-placeholder,
.uni-textarea-line,
.uni-textarea-compute,
.uni-textarea-textarea {
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
  text-decoration: inherit;
}
.uni-textarea-wrapper {
  display: block;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: inherit;
  overflow-y: hidden;
}
.uni-textarea-placeholder,
.uni-textarea-line,
.uni-textarea-compute,
.uni-textarea-textarea {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  white-space: inherit;
  word-break: inherit;
}
.uni-textarea-placeholder {
  color: grey;
  overflow: hidden;
}
.uni-textarea-line,
.uni-textarea-compute {
  visibility: hidden;
  height: auto;
}
.uni-textarea-line {
  width: 1em;
}
.uni-textarea-textarea {
  resize: none;
  background: none;
  color: inherit;
  opacity: 1;
  font: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  text-align: inherit;
  text-indent: inherit;
  text-transform: inherit;
  text-shadow: inherit;
}
/* 用于解决 iOS textarea 内部默认边距 */
.uni-textarea-textarea-fix-margin {
  width: auto;
  right: 0;
  margin: 0 -3px;
}
.uni-textarea-textarea:disabled {
  /* 用于重置iOS14以下禁用状态文字颜色 */
  -webkit-text-fill-color: currentcolor;
}
