{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <!-- 页面头部区域 -->\n    <view class=\"login-header\">\n      <image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\" />\n      <text class=\"app-name\">{{ appName }}</text>\n      <text class=\"app-desc\">{{ appDescription }}</text>\n    </view>\n\n    <!-- 登录表单区域 -->\n    <view class=\"login-form\">\n      <!-- 用户协议确认 -->\n      <view class=\"agreement-section\">\n        <checkbox-group @change=\"onAgreementChange\">\n          <label class=\"agreement-item\">\n            <checkbox :checked=\"agreedToTerms\" color=\"#4caf50\" />\n            <view class=\"agreement-text\">\n              <text>我已阅读并同意</text>\n              <text class=\"agreement-link\" @tap=\"showUserAgreement\">《用户服务协议》</text>\n              <text>和</text>\n              <text class=\"agreement-link\" @tap=\"showPrivacyPolicy\">《隐私政策》</text>\n            </view>\n          </label>\n        </checkbox-group>\n      </view>\n\n      <!-- 微信登录按钮 -->\n      <uv-button\n        type=\"primary\"\n        :disabled=\"!agreedToTerms || isLoading\"\n        :loading=\"isLoading\"\n        loadingText=\"登录中...\"\n        @click=\"handleWxLogin\"\n        :customStyle=\"loginButtonStyle\"\n      >\n        <uv-icon name=\"weixin\" color=\"#1976d2\" size=\"40\" style=\"margin-right: 16rpx;\" />\n        微信授权登录\n      </uv-button>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed } from 'vue';\nimport { storeToRefs } from 'pinia';\nimport { useUserStore } from '@/src/stores/modules/user';\nimport { wxLogin } from '@/src/api/modules/user';\nimport type { LoginParams, UserInfo } from '@/src/types/api';\n\n// ==================== Store ====================\nconst userStore = useUserStore();\nconst { profile } = storeToRefs(userStore);\nconst { setProfile } = userStore;\n\n// ==================== 响应式数据 ====================\n/** 是否同意用户协议 */\nconst agreedToTerms = ref<boolean>(false);\n/** 登录加载状态 */\nconst isLoading = ref<boolean>(false);\n\n// ==================== 计算属性 ====================\n/** 应用名称 */\nconst appName = computed(() => '疾控医护考试系统');\n/** 应用描述 */\nconst appDescription = computed(() => '专业、权威、便捷的任职资格考试平台');\n/** 登录按钮样式 */\nconst loginButtonStyle = computed(() => ({\n  width: '100%',\n  height: '88rpx',\n  backgroundColor: '#ffffff',\n  color: agreedToTerms.value ? '#1976d2' : 'rgba(25, 118, 210, 0.5)',\n  fontSize: '32rpx',\n  fontWeight: '500',\n  borderRadius: '16rpx',\n}));\n\n// ==================== 事件处理 ====================\n/**\n * 协议勾选状态变化\n * @param value 勾选状态数组\n */\nfunction onAgreementChange(value: string[]): void {\n  agreedToTerms.value = value.length > 0;\n}\n\n/**\n * 显示用户服务协议\n */\nfunction showUserAgreement(): void {\n  uni.showModal({\n    title: '用户服务协议',\n    content: `\n1. 本系统为疾控机构专用的任职资格考试平台\n2. 用户需提供真实有效的个人信息\n3. 考试过程中需遵守相关规定\n4. 系统会记录用户的学习和考试行为\n5. 用户信息将严格保密，仅用于考试管理\n\n详细协议内容请联系管理员获取。\n    `,\n    showCancel: false,\n    confirmText: '我知道了',\n  });\n}\n\n/**\n * 显示隐私政策\n */\nfunction showPrivacyPolicy(): void {\n  uni.showModal({\n    title: '隐私政策',\n    content: `\n1. 我们收集的信息：微信基本信息、个人资料、考试记录\n2. 信息用途：身份验证、考试管理、成绩统计\n3. 信息保护：采用加密存储，严格权限控制\n4. 信息共享：仅与相关机构共享必要信息\n5. 用户权利：可查看、修改个人信息\n\n详细政策内容请联系管理员获取。\n    `,\n    showCancel: false,\n    confirmText: '我知道了',\n  });\n}\n\n/**\n * 微信授权登录\n */\nasync function handleWxLogin(): Promise<void> {\n  // 检查协议同意状态\n  if (!agreedToTerms.value) {\n    uni.showToast({\n      title: '请先同意用户协议',\n      icon: 'none',\n      duration: 2000,\n    });\n    return;\n  }\n\n  isLoading.value = true;\n\n  try {\n    // 调用微信登录获取code\n    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {\n      uni.login({\n        provider: 'weixin',\n        success: resolve,\n        fail: reject,\n      });\n    });\n\n    // 构造登录参数\n    const loginParams: LoginParams = {\n      code: loginResult.code,\n    };\n\n    // 调用后端登录接口\n    const userInfo: UserInfo = await wxLogin(loginParams);\n\n    // 保存用户信息到Store\n    setProfile(userInfo);\n\n    // 登录成功提示\n    uni.showToast({\n      title: '登录成功',\n      icon: 'success',\n      duration: 1500,\n    });\n\n    // 根据用户状态进行页面跳转\n    setTimeout(() => {\n      navigateByUserStatus(userInfo.status);\n    }, 1500);\n\n  } catch (error) {\n    uni.__f__('error','at pages/login/login.vue:176','微信登录失败:', error);\n    uni.showToast({\n      title: '登录失败，请重试',\n      icon: 'none',\n      duration: 2000,\n    });\n  } finally {\n    isLoading.value = false;\n  }\n}\n\n/**\n * 根据用户状态进行页面跳转\n * @param status 用户状态\n */\nfunction navigateByUserStatus(status: UserInfo['status']): void {\n  switch (status) {\n    case 'approved':\n      // 已审核通过的正式用户，跳转到信息中心\n      uni.reLaunch({ url: '/pages/info/info' });\n      break;\n    case 'pending':\n      // 待审核用户，跳转到个人中心查看审核状态\n      uni.reLaunch({ url: '/pages/profile/profile' });\n      break;\n    case 'rejected':\n      // 审核未通过用户，跳转到个人中心修改资料\n      uni.reLaunch({ url: '/pages/profile/profile' });\n      break;\n    case 'incomplete':\n    default:\n      // 未提交资料的新用户，跳转到注册页面\n      uni.navigateTo({ url: '/pages/register/register' });\n      break;\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\n/* ==================== 主容器 ==================== */\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: $spacing-xl;\n  position: relative;\n}\n\n/* ==================== 头部区域 ==================== */\n.login-header {\n  text-align: center;\n  margin-bottom: $spacing-xl * 2;\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.logo {\n  width: 160rpx;\n  height: 160rpx;\n  margin-bottom: $spacing-lg;\n  border-radius: 50%;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n}\n\n.app-name {\n  display: block;\n  margin-bottom: $spacing-sm;\n  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.app-desc {\n  display: block;\n  opacity: 0.9;\n}\n\n/* ==================== 表单区域 ==================== */\n.login-form {\n  width: 100%;\n  max-width: 640rpx;\n  animation: fadeInUp 0.8s ease-out 0.2s both;\n}\n\n/* ==================== 协议区域 ==================== */\n.agreement-section {\n  margin-bottom: $spacing-xl;\n  padding: $spacing-md;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: $border-radius-medium;\n  backdrop-filter: blur(10rpx);\n}\n\n.agreement-text {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  line-height: $line-height-loose;\n  margin-left: $spacing-sm;\n}\n\n.agreement-link {\n  text-decoration: underline;\n  margin: 0 4rpx;\n}\n\n/* ==================== 动画效果 ==================== */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(60rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* ==================== 响应式适配 ==================== */\n@media screen and (max-width: 750rpx) {\n  .login-container {\n    padding: $spacing-lg;\n  }\n\n  .logo {\n    width: 140rpx;\n    height: 140rpx;\n  }\n}\n\n/* ==================== 深色模式适配 ==================== */\n@media (prefers-color-scheme: dark) {\n  .agreement-section {\n    background: rgba(0, 0, 0, 0.2);\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "storeToRefs", "ref", "computed", "uni", "wxL<PERSON>in"], "mappings": ";;;;;;;;;;;;;AAkDA,UAAM,YAAYA,wBAAAA;AACEC,kBAAAA,YAAY,SAAS;AACnC,UAAA,EAAE,WAAe,IAAA;AAIjB,UAAA,gBAAgBC,kBAAa,KAAK;AAElC,UAAA,YAAYA,kBAAa,KAAK;AAI9B,UAAA,UAAUC,cAAAA,SAAS,MAAM,UAAU;AAEnC,UAAA,iBAAiBA,cAAAA,SAAS,MAAM,mBAAmB;AAEnD,UAAA,mBAAmBA,cAAAA,SAAS,OAAO;AAAA,MACvC,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,OAAO,cAAc,QAAQ,YAAY;AAAA,MACzC,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,IACd,EAAA;AAOF,aAAS,kBAAkB,OAAuB;AAClC,oBAAA,QAAQ,MAAM,SAAS;AAAA,IACvC;AAKA,aAAS,oBAA0B;AACjCC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAST,YAAY;AAAA,QACZ,aAAa;AAAA,MAAA,CACd;AAAA,IACH;AAKA,aAAS,oBAA0B;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAST,YAAY;AAAA,QACZ,aAAa;AAAA,MAAA,CACd;AAAA,IACH;AAKA,mBAAe,gBAA+B;AAExC,UAAA,CAAC,cAAc,OAAO;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AACD;AAAA,MACF;AAEA,gBAAU,QAAQ;AAEd,UAAA;AAEF,cAAM,cAAc,MAAM,IAAI,QAAyB,CAAC,SAAS,WAAW;AAC1EA,wBAAAA,MAAI,MAAM;AAAA,YACR,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,UAAA,CACP;AAAA,QAAA,CACF;AAGD,cAAM,cAA2B;AAAA,UAC/B,MAAM,YAAY;AAAA,QAAA;AAId,cAAA,WAAqB,MAAMC,6BAAQ,WAAW;AAGpD,mBAAW,QAAQ;AAGnBD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAGD,mBAAW,MAAM;AACf,+BAAqB,SAAS,MAAM;AAAA,WACnC,IAAI;AAAA,eAEA,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,WAAW,KAAK;AACjEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MAAA,UACD;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AAMA,aAAS,qBAAqB,QAAkC;AAC9D,cAAQ,QAAQ;AAAA,QACd,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,mBAAoB,CAAA;AACxC;AAAA,QACF,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,yBAA0B,CAAA;AAC9C;AAAA,QACF,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,yBAA0B,CAAA;AAC9C;AAAA,QACF,KAAK;AAAA,QACL;AAEEA,wBAAAA,MAAI,WAAW,EAAE,KAAK,2BAA4B,CAAA;AAClD;AAAA,MACJ;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjNA,GAAG,WAAW,eAAe;"}