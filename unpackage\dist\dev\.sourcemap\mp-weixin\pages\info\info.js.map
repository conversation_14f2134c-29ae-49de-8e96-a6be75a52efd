{"version": 3, "file": "info.js", "sources": ["pages/info/info.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5mby9pbmZvLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"info-container\">\n    <!-- 非正式用户状态提示 -->\n    <view v-if=\"!userStore.isApproved\" class=\"status-tip\">\n      <view class=\"tip-content\">\n        <text class=\"tip-icon\">⚠️</text>\n        <view class=\"tip-text\">\n          <text class=\"tip-title\">{{ getStatusTipTitle() }}</text>\n          <text class=\"tip-desc\">{{ getStatusTipDesc() }}</text>\n        </view>\n        <button v-if=\"userStore.isIncomplete\" class=\"tip-btn\" @tap=\"goToRegister\">\n          去完善资料\n        </button>\n      </view>\n    </view>\n    \n    <!-- 正式用户内容 -->\n    <view v-else class=\"content-section\">\n      <!-- 轮播公告区 -->\n      <view class=\"banner-section\">\n        <swiper class=\"banner-swiper\" indicator-dots circular autoplay>\n          <swiper-item v-for=\"item in bannerList\" :key=\"item.id\">\n            <view class=\"banner-item\" @tap=\"goToDetail(item)\">\n              <text class=\"banner-title\">{{ item.title }}</text>\n            </view>\n          </swiper-item>\n        </swiper>\n      </view>\n      \n      <!-- 信息列表 -->\n      <view class=\"info-list\">\n        <!-- 公告列表 -->\n        <view class=\"info-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">📢 最新公告</text>\n            <text class=\"section-more\" @tap=\"goToList('announcement')\">更多</text>\n          </view>\n          <view class=\"info-items\">\n            <view \n              v-for=\"item in announcementList\" \n              :key=\"item.id\"\n              class=\"info-item\"\n              @tap=\"goToDetail(item)\"\n            >\n              <view class=\"item-header\">\n                <text class=\"item-title\">{{ item.title }}</text>\n                <text v-if=\"item.isTop\" class=\"top-tag\">置顶</text>\n              </view>\n              <text class=\"item-time\">{{ formatTime(item.publishTime) }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 政策法规 -->\n        <view class=\"info-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">📋 政策法规</text>\n            <text class=\"section-more\" @tap=\"goToList('policy')\">更多</text>\n          </view>\n          <view class=\"info-items\">\n            <view \n              v-for=\"item in policyList\" \n              :key=\"item.id\"\n              class=\"info-item\"\n              @tap=\"goToDetail(item)\"\n            >\n              <view class=\"item-header\">\n                <text class=\"item-title\">{{ item.title }}</text>\n              </view>\n              <text class=\"item-time\">{{ formatTime(item.publishTime) }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 重要通知 -->\n        <view class=\"info-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">🔔 重要通知</text>\n            <text class=\"section-more\" @tap=\"goToList('notice')\">更多</text>\n          </view>\n          <view class=\"info-items\">\n            <view \n              v-for=\"item in noticeList\" \n              :key=\"item.id\"\n              class=\"info-item\"\n              @tap=\"goToDetail(item)\"\n            >\n              <view class=\"item-header\">\n                <text class=\"item-title\">{{ item.title }}</text>\n              </view>\n              <text class=\"item-time\">{{ formatTime(item.publishTime) }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue';\nimport { useUserStore } from '../../src/stores/modules/user';\nimport { storeToRefs } from 'pinia';\nimport { getAnnouncementList, getPolicyList, getNoticeList } from '../../src/api/modules/info';\nimport type { InfoItem } from '../../src/types/api';\n\nconst userStore = useUserStore();\nconst { isApproved, isIncomplete, isPending, isRejected } = storeToRefs(userStore);\n\n// 响应式数据\nconst bannerList = ref<InfoItem[]>([]);\nconst announcementList = ref<InfoItem[]>([]);\nconst policyList = ref<InfoItem[]>([]);\nconst noticeList = ref<InfoItem[]>([]);\n\nonMounted(() => {\n  if (userStore.isApproved) {\n    loadInfoData();\n  }\n});\n\n/**\n * 加载信息数据\n */\nasync function loadInfoData() {\n  try {\n    // 并行加载各类信息\n    const [announcements, policies, notices] = await Promise.all([\n      getAnnouncementList({ page: 1, pageSize: 5 }),\n      getPolicyList({ page: 1, pageSize: 3 }),\n      getNoticeList({ page: 1, pageSize: 3 }),\n    ]);\n    \n    announcementList.value = announcements;\n    policyList.value = policies;\n    noticeList.value = notices;\n    \n    // 提取置顶公告作为轮播\n    bannerList.value = announcements.filter(item => item.isTop).slice(0, 3);\n  } catch (error) {\n    uni.__f__('error','at pages/info/info.vue:141','加载信息数据失败:', error);\n  }\n}\n\n/**\n * 获取状态提示标题\n */\nfunction getStatusTipTitle() {\n  if (userStore.isIncomplete) return '请完善个人资料';\n  if (userStore.isPending) return '资料审核中';\n  if (userStore.isRejected) return '资料审核未通过';\n  return '身份认证异常';\n}\n\n/**\n * 获取状态提示描述\n */\nfunction getStatusTipDesc() {\n  if (userStore.isIncomplete) return '完善个人资料后即可查看最新信息';\n  if (userStore.isPending) return '请耐心等待机构管理员审核';\n  if (userStore.isRejected) return '请修改资料后重新提交';\n  return '请联系管理员处理';\n}\n\n/**\n * 跳转到注册页面\n */\nfunction goToRegister() {\n  uni.navigateTo({ url: '/pages/register/register' });\n}\n\n/**\n * 跳转到信息列表\n */\nfunction goToList(type: string) {\n  uni.navigateTo({ url: `/pages/info/list?type=${type}` });\n}\n\n/**\n * 跳转到信息详情\n */\nfunction goToDetail(item: InfoItem) {\n  uni.navigateTo({ url: `/pages/info/detail?id=${item.id}` });\n}\n\n/**\n * 格式化时间\n */\nfunction formatTime(timeStr: string) {\n  const date = new Date(timeStr);\n  const now = new Date();\n  const diff = now.getTime() - date.getTime();\n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  \n  if (days === 0) return '今天';\n  if (days === 1) return '昨天';\n  if (days < 7) return `${days}天前`;\n  \n  return date.toLocaleDateString();\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\n.info-container {\n  min-height: 100vh;\n  background-color: $background-color;\n}\n\n.status-tip {\n  margin: $spacing-lg;\n  \n  .tip-content {\n    background-color: $surface-color;\n    border-radius: $border-radius-medium;\n    padding: $spacing-lg;\n    display: flex;\n    align-items: center;\n    box-shadow: $shadow-light;\n    \n    .tip-icon {\n      font-size: $font-size-xl;\n      margin-right: $spacing-md;\n    }\n    \n    .tip-text {\n      flex: 1;\n      \n      .tip-title {\n        display: block;\n        font-size: $font-size-lg;\n        font-weight: $font-weight-medium;\n        color: $text-primary;\n        margin-bottom: $spacing-xs;\n      }\n      \n      .tip-desc {\n        font-size: $font-size-sm;\n        color: $text-secondary;\n      }\n    }\n    \n    .tip-btn {\n      background-color: $primary-color;\n      color: white;\n      border: none;\n      border-radius: $border-radius-small;\n      padding: $spacing-xs $spacing-sm;\n      font-size: $font-size-sm;\n    }\n  }\n}\n\n.content-section {\n  .banner-section {\n    margin: $spacing-md;\n    \n    .banner-swiper {\n      height: 200rpx;\n      border-radius: $border-radius-medium;\n      overflow: hidden;\n      \n      .banner-item {\n        background: linear-gradient(135deg, $primary-color, $primary-light);\n        height: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: $spacing-md;\n        \n        .banner-title {\n          color: white;\n          font-size: $font-size-lg;\n          font-weight: $font-weight-medium;\n          text-align: center;\n        }\n      }\n    }\n  }\n  \n  .info-list {\n    .info-section {\n      margin: $spacing-md;\n      background-color: $surface-color;\n      border-radius: $border-radius-medium;\n      overflow: hidden;\n      box-shadow: $shadow-light;\n      \n      .section-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: $spacing-md;\n        border-bottom: 1rpx solid $divider-color;\n        \n        .section-title {\n          font-size: $font-size-lg;\n          font-weight: $font-weight-medium;\n          color: $text-primary;\n        }\n        \n        .section-more {\n          font-size: $font-size-sm;\n          color: $primary-color;\n        }\n      }\n      \n      .info-items {\n        .info-item {\n          padding: $spacing-md;\n          border-bottom: 1rpx solid $divider-color;\n          \n          &:last-child {\n            border-bottom: none;\n          }\n          \n          .item-header {\n            display: flex;\n            align-items: center;\n            margin-bottom: $spacing-xs;\n            \n            .item-title {\n              flex: 1;\n              font-size: $font-size-md;\n              color: $text-primary;\n              line-height: $line-height-normal;\n            }\n            \n            .top-tag {\n              background-color: $error-color;\n              color: white;\n              font-size: $font-size-xs;\n              padding: 2rpx 8rpx;\n              border-radius: $border-radius-small;\n              margin-left: $spacing-sm;\n            }\n          }\n          \n          .item-time {\n            font-size: $font-size-xs;\n            color: $text-disabled;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/info/info.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "storeToRefs", "ref", "onMounted", "getAnnouncementList", "getPolicyList", "getNoticeList", "uni"], "mappings": ";;;;;;;AA0GA,UAAM,YAAYA,wBAAAA;AAC0CC,kBAAAA,YAAY,SAAS;AAG3E,UAAA,aAAaC,kBAAgB,CAAA,CAAE;AAC/B,UAAA,mBAAmBA,kBAAgB,CAAA,CAAE;AACrC,UAAA,aAAaA,kBAAgB,CAAA,CAAE;AAC/B,UAAA,aAAaA,kBAAgB,CAAA,CAAE;AAErCC,kBAAAA,UAAU,MAAM;AACd,UAAI,UAAU,YAAY;AACX;MACf;AAAA,IAAA,CACD;AAKD,mBAAe,eAAe;AACxB,UAAA;AAEF,cAAM,CAAC,eAAe,UAAU,OAAO,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC3DC,qBAAAA,oBAAoB,EAAE,MAAM,GAAG,UAAU,GAAG;AAAA,UAC5CC,qBAAAA,cAAc,EAAE,MAAM,GAAG,UAAU,GAAG;AAAA,UACtCC,qBAAAA,cAAc,EAAE,MAAM,GAAG,UAAU,GAAG;AAAA,QAAA,CACvC;AAED,yBAAiB,QAAQ;AACzB,mBAAW,QAAQ;AACnB,mBAAW,QAAQ;AAGR,mBAAA,QAAQ,cAAc,OAAO,CAAA,SAAQ,KAAK,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,eAC/D,OAAO;AACdC,sBAAA,MAAI,MAAM,SAAQ,8BAA6B,aAAa,KAAK;AAAA,MACnE;AAAA,IACF;AAKA,aAAS,oBAAoB;AAC3B,UAAI,UAAU;AAAqB,eAAA;AACnC,UAAI,UAAU;AAAkB,eAAA;AAChC,UAAI,UAAU;AAAmB,eAAA;AAC1B,aAAA;AAAA,IACT;AAKA,aAAS,mBAAmB;AAC1B,UAAI,UAAU;AAAqB,eAAA;AACnC,UAAI,UAAU;AAAkB,eAAA;AAChC,UAAI,UAAU;AAAmB,eAAA;AAC1B,aAAA;AAAA,IACT;AAKA,aAAS,eAAe;AACtBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,2BAA4B,CAAA;AAAA,IACpD;AAKA,aAAS,SAAS,MAAc;AAC9BA,oBAAA,MAAI,WAAW,EAAE,KAAK,yBAAyB,IAAI,IAAI;AAAA,IACzD;AAKA,aAAS,WAAW,MAAgB;AAClCA,0BAAI,WAAW,EAAE,KAAK,yBAAyB,KAAK,EAAE,IAAI;AAAA,IAC5D;AAKA,aAAS,WAAW,SAAiB;AAC7B,YAAA,OAAO,IAAI,KAAK,OAAO;AACvB,YAAA,0BAAU;AAChB,YAAM,OAAO,IAAI,QAAQ,IAAI,KAAK,QAAQ;AAC1C,YAAM,OAAO,KAAK,MAAM,QAAQ,MAAO,KAAK,KAAK,GAAG;AAEpD,UAAI,SAAS;AAAU,eAAA;AACvB,UAAI,SAAS;AAAU,eAAA;AACvB,UAAI,OAAO;AAAG,eAAO,GAAG,IAAI;AAE5B,aAAO,KAAK;IACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtMA,GAAG,WAAW,eAAe;"}