/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
/* ==================== 主容器 ==================== */
.register-container.data-v-bac4a35d {
  min-height: 100vh;
  background-color: #fafafa;
  padding: 32rpx;
}

/* ==================== 头部区域 ==================== */
.register-header.data-v-bac4a35d {
  text-align: center;
  margin-bottom: 48rpx;
}
.page-title.data-v-bac4a35d {
  display: block;
  font-size: 36rpx;
  color: #1976d2;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.page-desc.data-v-bac4a35d {
  display: block;
  font-size: 28rpx;
  color: #757575;
}

/* ==================== 表单区域 ==================== */
.register-form.data-v-bac4a35d {
  margin-bottom: 48rpx;
}

/* ==================== 头像上传 ==================== */
.avatar-upload.data-v-bac4a35d {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 2rpx dashed #e0e0e0;
}
.upload-placeholder.data-v-bac4a35d {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
}
.upload-text.data-v-bac4a35d {
  font-size: 26rpx;
  color: #999999;
  margin-top: 16rpx;
}
.upload-desc.data-v-bac4a35d {
  font-size: 22rpx;
  color: #cccccc;
  margin-top: 8rpx;
}
.avatar-preview.data-v-bac4a35d {
  width: 100%;
  height: 100%;
  position: relative;
}
.avatar-image.data-v-bac4a35d {
  width: 100%;
  height: 100%;
}
.avatar-mask.data-v-bac4a35d {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.avatar-preview:active .avatar-mask.data-v-bac4a35d {
  opacity: 1;
}

/* ==================== 操作按钮 ==================== */
.register-actions.data-v-bac4a35d {
  padding: 24rpx 0;
}